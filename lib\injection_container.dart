import 'package:aquapartner/core/cache/cache_manager.dart';
import 'package:aquapartner/data/datasources/local/smr_report_local_datasource.dart';
import 'package:aquapartner/data/entities/farmer_visits/visit_entity.dart';
import 'package:aquapartner/data/repositories/farmer_repository_impl.dart';
import 'package:aquapartner/data/repositories/sales_order_repository_impl.dart';
import 'package:aquapartner/domain/repositories/farmer_repository.dart';
import 'package:aquapartner/domain/usecases/sales_order/check_if_sync_needed_usecase.dart';
import 'package:aquapartner/domain/usecases/sales_order/get_sales_orders_usecase.dart';
import 'package:aquapartner/domain/usecases/sales_order/sync_sales_orders_usecase.dart';
import 'package:aquapartner/presentation/cubit/billing_and_payments/billing_and_payments_cubit.dart';
import 'package:aquapartner/presentation/cubit/dues/dues_cubit.dart';
import 'package:aquapartner/presentation/cubit/home/<USER>';
import 'package:aquapartner/presentation/cubit/sales_order/sales_order_cubit.dart';
import 'package:aquapartner/presentation/cubit/scheme/scheme_cubit.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_performance/firebase_performance.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get_it/get_it.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'core/constants/app_constants.dart';
import 'core/database/objectbox_config.dart';
import 'core/network/api_client.dart';
import 'core/network/app_check_interceptor.dart';
import 'core/network/mongodb_connection_manager.dart';
import 'core/network/performance_interceptor.dart';
import 'core/routes/analytics_route_observer.dart';
import 'core/services/analytics_service.dart';
import 'core/services/feature_usage_tracker.dart';
import 'core/services/app_check_service.dart';
import 'core/services/mongodb_service.dart';
import 'core/services/navigation_service.dart';
import 'core/services/performance_monitoring_service.dart';
import 'core/services/session_manager.dart';
import 'data/datasources/device_info_data_source.dart';
import 'data/datasources/local/account_statement_local_data_source.dart';
import 'data/datasources/local/credit_notes_local_data_source.dart';
import 'data/datasources/local/customer_local_data_source.dart';
import 'data/datasources/local/customer_payments_local_data_source.dart';
import 'data/datasources/local/customer_scheme_local_data_source.dart';
import 'data/datasources/local/dashboard_local_datasource.dart';
import 'data/datasources/local/dues_local_data_source.dart';
import 'data/datasources/local/farmer_local_datasource.dart';
import 'data/datasources/local/interested_product_local_datasource.dart';
import 'data/datasources/local/invoice_local_data_source.dart';
import 'data/datasources/local/price_list_local_data_source.dart';
import 'data/datasources/local/product_catalogue_local_datasource.dart';
import 'data/datasources/local/sales_order_local_datasource.dart';
import 'data/datasources/local/user_local_data_source.dart';
import 'data/datasources/remote/account_statement_remote_data_source.dart';
import 'data/datasources/remote/auth_remote_data_source.dart';
import 'data/datasources/remote/credit_notes_remote_data_source.dart';
import 'data/datasources/remote/customer_payments_remote_data_source.dart';
import 'data/datasources/remote/customer_remote_data_source.dart';
import 'data/datasources/remote/customer_scheme_remote_data_source.dart';
import 'data/datasources/remote/dashboard_remote_datasource.dart';
import 'data/datasources/remote/dues_remote_data_source.dart';
import 'data/datasources/remote/interested_product_remote_datasource.dart';
import 'data/datasources/remote/invoice_remote_data_source.dart';
import 'data/datasources/remote/invoice_status_remote_data_source.dart';
import 'data/datasources/remote/payment_remote_data_source.dart';
import 'data/datasources/remote/zoho_books_payment_remote_data_source.dart';
import 'data/datasources/remote/mongodb_datasource.dart';
import 'data/datasources/remote/farmer_remote_datasource.dart';
import 'data/datasources/remote/price_list_remote_data_source.dart';
import 'data/datasources/remote/product_catalogue_remote_datasource.dart';
import 'data/datasources/remote/firebase_remote_config_data_source.dart';
import 'data/datasources/remote/sales_order_remote_datasource.dart';
import 'data/datasources/remote/smr_report_remote_datasource.dart';
import 'data/datasources/remote/user_remote_data_source.dart';
import 'data/entities/farmer_visits/farmer_entity.dart';
import 'data/models/account_statement/account_statement_models.dart';
import 'data/models/credit_notes/credit_note_model.dart';
import 'data/models/customer_scheme_model.dart';
import 'data/models/dashboard/dashboard_model.dart';
import 'data/models/dues/dues_model.dart';
import 'data/models/invoices/objectbox_invoice_model.dart';
import 'data/models/objectbox_customer_model.dart';
import 'data/models/objectbox_interested_product_model.dart';
import 'data/models/objectbox_price_list_model.dart';
import 'data/models/objectbox_product_catalogue_model.dart';
import 'data/models/objectbox_smr_report_model.dart';
import 'data/models/payments/customer_payment_model.dart';
import 'data/models/sales_order/sales_order_item_model.dart';
import 'data/models/sales_order/sales_order_model.dart';
import 'data/repositories/account_statement_repository_impl.dart';
import 'data/repositories/auth_repository_impl.dart';
import 'data/repositories/credit_notes_repository_impl.dart';
import 'data/repositories/customer_payments_repository_impl.dart';
import 'data/repositories/customer_repository_impl.dart';
import 'data/repositories/customer_scheme_repository_impl.dart';
import 'data/repositories/dashboard_repository_impl.dart';
import 'data/repositories/dues_repository_impl.dart';
import 'data/repositories/interested_product_repository_impl.dart';
import 'data/repositories/invoice_repository_impl.dart';
import 'data/repositories/invoice_status_repository_impl.dart';
import 'data/repositories/payment_repository_impl.dart';
import 'data/repositories/zoho_books_payment_repository_impl.dart';
import 'data/repositories/price_list_repository_impl.dart';
import 'data/repositories/product_catalogue_repository_impl.dart';
import 'data/repositories/smr_report_repository_impl.dart';
import 'data/repositories/user_repository_impl.dart';
import 'data/repositories/version_repository_impl.dart';
import 'domain/repositories/account_statement_repository.dart';
import 'domain/repositories/auth_repository.dart';
import 'domain/repositories/credit_notes_repository.dart';
import 'domain/repositories/customer_payments_repository.dart';
import 'domain/repositories/customer_repository.dart';
import 'domain/repositories/customer_scheme_repository.dart';
import 'domain/repositories/dashboard_repository.dart';
import 'domain/repositories/dues_repository.dart';
import 'domain/repositories/interested_product_repository.dart';
import 'domain/repositories/invoice_repository.dart';
import 'domain/repositories/invoice_status_repository.dart';
import 'domain/repositories/payment_repository.dart';
import 'domain/repositories/zoho_books_payment_repository.dart';
import 'domain/repositories/price_list_repository.dart';
import 'domain/repositories/product_catalogue_repository.dart';
import 'domain/repositories/sales_order_repository.dart';
import 'domain/repositories/smr_report_repository.dart';
import 'domain/repositories/user_repository.dart';
import 'domain/repositories/version_repository.dart';
import 'domain/services/auth_service.dart';
import 'domain/services/dashboard_service.dart';
import 'domain/usecases/account_statement/check_if_sync_needed_usecase.dart';
import 'domain/usecases/account_statement/get_account_statement_usecase.dart';
import 'domain/usecases/account_statement/invalidate_account_statement_cache_usecase.dart';
import 'domain/usecases/account_statement/sync_account_statement_usecase.dart';
import 'domain/usecases/auth_usecases.dart';
import 'domain/usecases/check_mandatory_update.dart';
import 'domain/usecases/credit_notes_usecases.dart';
import 'domain/usecases/customer_payments_usecases.dart';
import 'domain/usecases/customer_usercases.dart';
import 'domain/usecases/payments/create_payment_session_usecase.dart';
import 'domain/usecases/payments/create_zoho_books_payment_session_usecase.dart';
import 'domain/usecases/payments/get_payment_status_usecase.dart';
import 'domain/usecases/payments/process_payment_usecase.dart';
import 'domain/usecases/dashboard_usecases.dart';
import 'domain/usecases/dues/check_if_dues_sync_needed_usecase.dart';
import 'domain/usecases/dues/get_dues_usecase.dart';
import 'domain/usecases/dues/sync_dues_usecase.dart';
import 'domain/usecases/get_all_farmers_usecase.dart';
import 'domain/usecases/interested_product_usecases.dart';
import 'domain/usecases/invoices/check_if_invoices_sync_needed_usecase.dart';
import 'domain/usecases/invoices/get_invoice_by_id_usecase.dart';
import 'domain/usecases/invoices/get_invoices.dart';
import 'domain/usecases/invoices/sync_invoices_usecase.dart';
import 'domain/usecases/invoices/update_invoice_status_use_case.dart';
import 'domain/usecases/price_list_usecases.dart';
import 'domain/usecases/product_catalogue_usecases.dart';
import 'domain/usecases/smr_reports_usecases.dart';
import 'domain/usecases/user_usecases.dart';
import 'domain/usecases/sync_usecases.dart';
import 'presentation/cubit/account_statement/account_statement_cubit.dart';
import 'presentation/cubit/auth/auth_cubit.dart';
import 'presentation/cubit/connectivity/connectivity_cubit.dart';
import 'presentation/cubit/credit_notes/credit_notes_cubit.dart';
import 'presentation/cubit/customer/customer_cubit.dart';
import 'presentation/cubit/dashboard/dashboard_cubit.dart';
import 'core/network/network_info.dart';
import 'core/utils/logger.dart';
import 'data/models/user_model.dart';
import 'objectbox.g.dart';
import 'presentation/cubit/interested_product/interested_product_cubit.dart';
import 'presentation/cubit/invoices/invoices_cubit.dart';
import 'presentation/cubit/my_farmers/farmer_visits_cubit.dart';
import 'presentation/cubit/navigation/navigation_cubit.dart';
import 'presentation/cubit/payments/customer_payments_cubit.dart';
import 'presentation/cubit/payments/payment_cubit.dart';
import 'presentation/cubit/price_list/price_list_cubit.dart';
import 'presentation/cubit/product_catalogue/product_catalogue_cubit.dart';
import 'presentation/cubit/smr_report/smr_report_cubit.dart';
import 'presentation/cubit/update_checker/update_checker_cubit.dart';

final sl = GetIt.instance;

Future<void> init() async {
  // External
  sl.registerLazySingleton(() => const FlutterSecureStorage());

  final firebaseAuth = FirebaseAuth.instance;
  await firebaseAuth.setSettings(appVerificationDisabledForTesting: true);

  sl.registerLazySingleton(() => firebaseAuth);
  sl.registerLazySingleton(() => FirebaseRemoteConfig.instance);
  sl.registerLazySingleton(() => FirebasePerformance.instance);
  sl.registerLazySingleton(() => FirebaseAppCheck.instance);
  sl.registerLazySingleton(() => InternetConnectionChecker.createInstance());
  sl.registerLazySingleton(() => Connectivity());

  final SharedPreferences prefs = await SharedPreferences.getInstance();

  sl.registerLazySingleton(() => prefs);

  // Register AppLogger
  sl.registerLazySingleton(() => AppLogger());
  final logger = sl<AppLogger>();

  sl.registerLazySingleton(() {
    final dio = Dio(
      BaseOptions(
        baseUrl: AppConstants.baseUrl,
        connectTimeout: const Duration(seconds: 15),
        receiveTimeout: const Duration(seconds: 15),
      ),
    );

    // Add logging interceptor in debug mode
    dio.interceptors.add(
      PrettyDioLogger(
        requestHeader: true,
        requestBody: true,
        responseBody: true,
        responseHeader: false,
        error: true,
        compact: true,
      ),
    );

    // Add performance monitoring interceptor
    dio.interceptors.add(
      PerformanceInterceptor(
        sl<PerformanceMonitoringService>(),
        sl<AppLogger>(),
      ),
    );

    // Add App Check interceptor
    dio.interceptors.add(
      AppCheckInterceptor(sl<AppCheckService>(), sl<AppLogger>()),
    );

    return dio;
  });

  // Services
  sl.registerLazySingleton<NetworkInfo>(
    () => NetworkInfoImpl(
      connectivity: sl<Connectivity>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton(
    () =>
        MongoDBService(networkInfo: sl<NetworkInfo>(), logger: sl<AppLogger>()),
  );

  sl.registerLazySingleton(() => NavigationService());

  sl.registerLazySingleton(
    () => ApiClient(sl<Dio>(), sl<FlutterSecureStorage>()),
  );

  sl.registerLazySingleton(
    () => DashboardService(
      getCustomerByMobileNumber: sl<GetCustomerByMobileNumber>(),
      getUserUseCase: sl<GetUserUseCase>(),
      getDashboardUseCase: sl<GetDashboardUseCase>(),
      logger: sl<AppLogger>(),
      dashboardRepository: sl<DashboardRepository>(),
    ),
  );

  sl.registerLazySingleton<AuthService>(
    () => AuthServiceImpl(
      getCustomerByMobileNumber: sl<GetCustomerByMobileNumber>(),
      getUserUseCase: sl<GetUserUseCase>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton(() => FirebaseAnalytics.instance);
  sl.registerLazySingleton(
    () => AnalyticsService(sl<FirebaseAnalytics>(), sl<AppLogger>()),
  );
  sl.registerLazySingleton(
    () => AnalyticsRouteObserver(sl<AnalyticsService>()),
  );

  // Register Performance Monitoring Service
  sl.registerLazySingleton(
    () => PerformanceMonitoringService(
      sl<FirebasePerformance>(),
      sl<AppLogger>(),
    ),
  );

  // Register App Check Service
  sl.registerLazySingleton(
    () => AppCheckService(sl<FirebaseAppCheck>(), sl<AppLogger>()),
  );

  sl.registerLazySingleton(
    () => CacheManager(
      sharedPreferences: sl<SharedPreferences>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton(() => SessionManager(sl<AnalyticsService>()));
  sl.registerLazySingleton(() => FeatureUsageTracker(sl<AnalyticsService>()));

  // Cubits
  sl.registerFactory(
    () => AuthCubit(
      sendOtpUseCase: sl<SendOtpUseCase>(),
      verifyOtpUseCase: sl<VerifyOtpUseCase>(),
      signOutUseCase: sl<SignOutUseCase>(),
      getUserUseCase: sl<GetUserUseCase>(),
      saveUserUseCase: sl<SaveUserUseCase>(),
      updateUserUseCase: sl<UpdateUserUseCase>(),
      syncUserUseCase: sl<SyncUserUseCase>(),
      getSyncStatusUseCase: sl<GetSyncStatusUseCase>(),
      logger: sl<AppLogger>(),
      checkAuthStatusUseCase: sl<CheckAuthStatusUseCase>(),
      getCustomerByMobileNumber: sl<GetCustomerByMobileNumber>(),
      analyticsService: sl<AnalyticsService>(),
    ),
  );

  sl.registerFactory(
    () => HomeCubit(
      sl<CheckIfSyncNeeded>(),
      sl<GetCustomerByMobileNumber>(),
      sl<SyncCustomerByCustomerId>(),
      sl<GetUserUseCase>(),
      sl<AppLogger>(),
      sl<AnalyticsService>(),
      sl<FeatureUsageTracker>(),
    ),
  );

  sl.registerFactory(
    () => ConnectivityCubit(
      connectivity: sl<Connectivity>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerFactory(() => NavigationCubit());

  sl.registerFactory(
    () => PriceListCubit(
      getPriceListsUseCase: sl(),
      syncPriceListsUseCase: sl(),
      logger: sl(),
    ),
  );

  sl.registerFactory(
    () => ProductCatalogueCubit(
      getProductCataloguesUseCase: sl<GetProductCataloguesUseCase>(),
      getProductCatalogueByNameUseCase: sl<GetProductCatalogueByNameUseCase>(),
      syncProductCataloguesUseCase: sl<SyncProductCataloguesUseCase>(),
      addInterestedProductUseCase: sl<AddInterestedProductUseCase>(),
      getUserUseCase: sl<GetUserUseCase>(),
      logger: sl<AppLogger>(),
      analyticsService: sl<AnalyticsService>(),
    ),
  );

  sl.registerFactory(
    () => InterestedProductCubit(
      getInterestedProductsUseCase: sl(),
      addInterestedProductUseCase: sl(),
      syncInterestedProductsUseCase: sl(),
      getUnsyncedCountUseCase: sl(),
      getUserUseCase: sl(),
      logger: sl(),
    ),
  );

  sl.registerFactory(
    () => UpdateCheckerCubit(
      checkMandatoryUpdate: sl<CheckMandatoryUpdate>(),
      logger: sl(),
    ),
  );

  sl.registerFactory(
    () => DashboardCubit(
      dashboardService: sl<DashboardService>(),
      getDashboardUseCase: sl<GetDashboardUseCase>(),
      syncDashboardUseCase: sl<SyncDashboardUseCase>(),
      getSyncStatusUseCase: sl<GetDashboardSyncStatusUseCase>(),
      logger: sl<AppLogger>(),
      authService: sl<AuthService>(),
      connectivityChecker: sl<InternetConnectionChecker>(),
      analyticsService: sl<AnalyticsService>(),
    ),
  );

  sl.registerFactory(
    () => CustomerCubit(
      checkIfSyncNeeded: sl<CheckIfSyncNeeded>(),
      logger: sl<AppLogger>(),
      authService: sl<AuthService>(),
    ),
  );

  sl.registerFactory(
    () => SchemeCubit(
      repository: sl<CustomerSchemeRepository>(),
      authService: sl<AuthService>(),
      logger: sl<AppLogger>(),
      connectivity: sl<Connectivity>(),
    ),
  );

  sl.registerFactory(
    () => AccountStatementCubit(
      getAccountStatementUseCase: sl<GetAccountStatementUseCase>(),
      syncAccountStatementUseCase: sl<SyncAccountStatementUseCase>(),
      checkIfSyncNeededUseCase: sl<CheckIfAccountStatementSyncNeededUseCase>(),
      invalidateCacheUseCase: sl<InvalidateAccountStatementCacheUseCase>(),
      authService: sl<AuthService>(),
      logger: sl<AppLogger>(),
      connectivity: sl<Connectivity>(),
    ),
  );

  sl.registerFactory(
    () => FarmerVisitsCubit(
      getAllFarmersUseCase: sl<GetAllFarmersUseCase>(),
      authService: sl<AuthService>(),
    ),
  );

  sl.registerFactory(
    () => SMRReportCubit(
      getSMRReportsUseCase: sl<GetSMRReportsUseCase>(),
      syncSMRReportsUseCase: sl<SyncSMRReportsUseCase>(),
      authService: sl<AuthService>(),
    ),
  );

  sl.registerFactory(() => BillingAndPaymentsPageCubit(sl<AnalyticsService>()));

  sl.registerFactory(
    () => SalesOrderCubit(
      getSalesOrdersUseCase: sl<GetSalesOrdersUseCase>(),
      syncSalesOrdersUseCase: sl<SyncSalesOrdersUseCase>(),
      checkIfSyncNeededUseCase: sl<CheckIfSalesOrdersSyncNeededUseCase>(),
      authService: sl<AuthService>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerFactory(
    () => DuesCubit(
      getDuesUseCase: sl<GetDuesUseCase>(),
      syncDuesUseCase: sl<SyncDuesUseCase>(),
      checkIfDuesSyncNeededUseCase: sl<CheckIfDuesSyncNeededUseCase>(),
      authService: sl<AuthService>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerFactory(
    () => InvoicesCubit(
      getInvoices: sl<GetInvoices>(),
      getInvoiceById: sl<GetInvoiceById>(),
      syncInvoices: sl<SyncInvoices>(),
      checkIfSyncNeeded: sl<CheckIfInvoicesSyncNeeded>(),
      logger: sl<AppLogger>(),
      authService: sl<AuthService>(),
    ),
  );

  sl.registerFactory(
    () => CustomerPaymentsCubit(
      getCustomerPayments: sl<GetCustomerPayments>(),
      syncCustomerPayments: sl<SyncCustomerPayments>(),
      getLocalCustomerPayments: sl<GetLocalCustomerPayments>(),
      authService: sl<AuthService>(),
    ),
  );

  sl.registerFactory(
    () => PaymentCubit(
      createPaymentSessionUseCase: sl<CreatePaymentSessionUseCase>(),
      processPaymentUseCase: sl<ProcessPaymentUseCase>(),
      getPaymentStatusUseCase: sl<GetPaymentStatusUseCase>(),
      createZohoBooksPaymentSessionUseCase:
          sl<CreateZohoBooksPaymentSessionUseCase>(),
      authService: sl<AuthService>(),
      logger: sl<AppLogger>(),
      analyticsService: sl<AnalyticsService>(),
    ),
  );

  sl.registerFactory(
    () => CreditNotesCubit(
      getCreditNotes: sl<GetCreditNotes>(),
      syncCreditNotes: sl<SyncCreditNotes>(),
      getLocalCreditNotes: sl<GetLocalCreditNotes>(),
      authService: sl<AuthService>(),
    ),
  );

  // Use cases
  sl.registerLazySingleton(() => SendOtpUseCase(sl()));
  sl.registerLazySingleton(() => VerifyOtpUseCase(sl()));
  sl.registerLazySingleton(() => SignOutUseCase(sl()));
  sl.registerLazySingleton(() => GetUserUseCase(sl()));
  sl.registerLazySingleton(() => SaveUserUseCase(sl()));
  sl.registerLazySingleton(() => UpdateUserUseCase(sl()));
  sl.registerLazySingleton(() => DeleteUserUseCase(sl()));
  sl.registerLazySingleton(
    () => CheckAuthStatusUseCase(
      repository: sl<UserRepository>(),
      logger: sl<AppLogger>(),
    ),
  );
  sl.registerLazySingleton(
    () => CheckMandatoryUpdate(sl<VersionRepository>(), sl<AppLogger>()),
  );
  sl.registerLazySingleton(
    () => SyncUserUseCase(repository: sl(), networkInfo: sl(), logger: sl()),
  );
  sl.registerLazySingleton(
    () => GetSyncStatusUseCase(repository: sl(), logger: sl()),
  );
  sl.registerLazySingleton(
    () => GetPriceListsUseCase(repository: sl(), logger: sl()),
  );
  sl.registerLazySingleton(
    () => SyncPriceListsUseCase(repository: sl(), logger: sl()),
  );
  sl.registerLazySingleton(
    () => GetPriceListByStateUseCase(repository: sl(), logger: sl()),
  );
  sl.registerLazySingleton(
    () => GetProductCataloguesUseCase(repository: sl(), logger: sl()),
  );
  sl.registerLazySingleton(
    () => GetProductCatalogueByNameUseCase(repository: sl(), logger: sl()),
  );
  sl.registerLazySingleton(
    () => SyncProductCataloguesUseCase(repository: sl(), logger: sl()),
  );
  sl.registerLazySingleton(
    () => GetInterestedProductsUseCase(repository: sl(), logger: sl()),
  );
  sl.registerLazySingleton(
    () => AddInterestedProductUseCase(repository: sl(), logger: sl()),
  );
  sl.registerLazySingleton(
    () => SyncInterestedProductsUseCase(repository: sl(), logger: sl()),
  );
  sl.registerLazySingleton(
    () => GetUnsyncedCountUseCase(repository: sl(), logger: sl()),
  );
  sl.registerLazySingleton(
    () => GetDashboardUseCase(
      repository: sl<DashboardRepository>(),
      logger: sl<AppLogger>(),
    ),
  );
  sl.registerLazySingleton(
    () => SyncDashboardUseCase(
      repository: sl<DashboardRepository>(),
      networkInfo: sl<NetworkInfo>(),
      logger: sl<AppLogger>(),
    ),
  );
  sl.registerLazySingleton(
    () => GetDashboardSyncStatusUseCase(
      repository: sl<DashboardRepository>(),
      logger: sl<AppLogger>(),
    ),
  );
  sl.registerLazySingleton(() => GetCustomerByMobileNumber(sl()));
  sl.registerLazySingleton(() => SyncCustomerByCustomerId(sl()));
  sl.registerLazySingleton(() => CheckIfSyncNeeded(sl()));
  sl.registerLazySingleton(
    () => GetAccountStatementUseCase(
      repository: sl<AccountStatementRepository>(),
      logger: sl<AppLogger>(),
    ),
  );
  sl.registerLazySingleton(
    () => SyncAccountStatementUseCase(
      repository: sl<AccountStatementRepository>(),
      logger: sl<AppLogger>(),
    ),
  );
  sl.registerLazySingleton(
    () => CheckIfAccountStatementSyncNeededUseCase(
      repository: sl<AccountStatementRepository>(),
      logger: sl<AppLogger>(),
    ),
  );
  sl.registerLazySingleton(
    () => InvalidateAccountStatementCacheUseCase(
      repository: sl<AccountStatementRepository>(),
      logger: sl<AppLogger>(),
    ),
  );
  sl.registerLazySingleton(() => GetAllFarmersUseCase(sl<FarmerRepository>()));
  sl.registerLazySingleton(
    () => GetSMRReportsUseCase(sl<SMRReportRepository>()),
  );
  sl.registerLazySingleton(
    () => SyncSMRReportsUseCase(sl<SMRReportRepository>()),
  );
  sl.registerLazySingleton(
    () => GetSalesOrdersUseCase(
      repository: sl<SalesOrderRepository>(),
      logger: sl<AppLogger>(),
    ),
  );
  sl.registerLazySingleton(
    () => SyncSalesOrdersUseCase(
      repository: sl<SalesOrderRepository>(),
      logger: sl<AppLogger>(),
      networkInfo: sl<NetworkInfo>(),
    ),
  );
  sl.registerLazySingleton(
    () => CheckIfSalesOrdersSyncNeededUseCase(
      repository: sl<SalesOrderRepository>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton(() => GetDuesUseCase(sl<DuesRepository>()));
  sl.registerLazySingleton(() => SyncDuesUseCase(sl<DuesRepository>()));
  sl.registerLazySingleton(
    () => CheckIfDuesSyncNeededUseCase(sl<DuesRepository>()),
  );

  sl.registerLazySingleton(() => GetInvoices(sl<InvoiceRepository>()));
  sl.registerLazySingleton(() => GetInvoiceById(sl<InvoiceRepository>()));
  sl.registerLazySingleton(() => SyncInvoices(sl<InvoiceRepository>()));
  sl.registerLazySingleton(
    () => CheckIfInvoicesSyncNeeded(sl<InvoiceRepository>()),
  );
  sl.registerLazySingleton(
    () => UpdateInvoiceStatusUseCase(sl<InvoiceStatusRepository>()),
  );

  sl.registerLazySingleton(
    () => GetCustomerPayments(sl<CustomerPaymentsRepository>()),
  );
  sl.registerLazySingleton(
    () => SyncCustomerPayments(sl<CustomerPaymentsRepository>()),
  );
  sl.registerLazySingleton(
    () => GetLocalCustomerPayments(sl<CustomerPaymentsRepository>()),
  );

  // Payment use cases
  sl.registerLazySingleton(
    () => CreatePaymentSessionUseCase(sl<PaymentRepository>()),
  );
  sl.registerLazySingleton(
    () => ProcessPaymentUseCase(sl<PaymentRepository>()),
  );
  sl.registerLazySingleton(
    () => GetPaymentStatusUseCase(sl<PaymentRepository>()),
  );

  // Zoho Books payment use cases
  sl.registerLazySingleton(
    () =>
        CreateZohoBooksPaymentSessionUseCase(sl<ZohoBooksPaymentRepository>()),
  );

  sl.registerLazySingleton(() => GetCreditNotes(sl<CreditNotesRepository>()));
  sl.registerLazySingleton(() => SyncCreditNotes(sl<CreditNotesRepository>()));
  sl.registerLazySingleton(
    () => GetLocalCreditNotes(sl<CreditNotesRepository>()),
  ); // Register local use case

  // Repository
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      localDataSource: sl<UserLocalDataSource>(),
      customerLocalDataSource: sl<CustomerLocalDataSource>(),
      dashboardLocalDataSource: sl<DashboardLocalDataSource>(),
      remoteDataSource: sl<AuthRemoteDataSource>(),
      networkInfo: sl<NetworkInfo>(),
      firebaseAuth: sl<FirebaseAuth>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton<UserRepository>(
    () => UserRepositoryImpl(
      localDataSource: sl<UserLocalDataSource>(),
      remoteDataSource: sl<UserRemoteDataSource>(),
      networkInfo: sl<NetworkInfo>(),
      logger: sl<AppLogger>(),
      mongoDBDataSource: sl<MongoDBDataSource>(),
    ),
  );

  sl.registerLazySingleton<PriceListRepository>(
    () => PriceListRepositoryImpl(
      remoteDataSource: sl<PriceListRemoteDataSource>(),
      localDataSource: sl<PriceListLocalDataSource>(),
      networkInfo: sl<NetworkInfo>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton<ProductCatalogueRepository>(
    () => ProductCatalogueRepositoryImpl(
      remoteDataSource: sl<ProductCatalogueRemoteDataSource>(),
      localDataSource: sl<ProductCatalogueLocalDataSource>(),
      networkInfo: sl<NetworkInfo>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton<InterestedProductRepository>(
    () => InterestedProductRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
      networkInfo: sl(),
      logger: sl(),
    ),
  );

  sl.registerLazySingleton<VersionRepository>(
    () => VersionRepositoryImpl(
      remoteConfigDataSource:
          sl<RemoteConfigDataSource>(), // Use the interface type
      deviceInfoDataSource: sl<DeviceInfoDataSource>(),
    ),
  );

  sl.registerLazySingleton<DashboardRepository>(
    () => DashboardRepositoryImpl(
      remoteDataSource: sl<DashboardRemoteDataSource>(),
      localDataSource: sl<DashboardLocalDataSource>(),
      networkInfo: sl<NetworkInfo>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton<CustomerRepository>(
    () => CustomerRepositoryImpl(
      remoteDataSource: sl<CustomerRemoteDataSource>(),
      localDataSource: sl<CustomerLocalDataSource>(),
      networkInfo: sl<NetworkInfo>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton<CustomerSchemeRepository>(
    () => CustomerSchemeRepositoryImpl(
      localDataSource: sl<CustomerSchemeLocalDataSource>(),
      remoteDataSource: sl<CustomerSchemeRemoteDataSource>(),
      connectivity: sl<Connectivity>(),
    ),
  );

  sl.registerLazySingleton<AccountStatementRepository>(
    () => AccountStatementRepositoryImpl(
      remoteDataSource: sl<AccountStatementRemoteDataSource>(),
      localDataSource: sl<AccountStatementLocalDataSource>(),
      networkInfo: sl<NetworkInfo>(),
      logger: sl<AppLogger>(),
      cacheManager: sl<CacheManager>(),
    ),
  );

  sl.registerLazySingleton<FarmerRepository>(
    () => FarmerRepositoryImpl(
      remoteDataSource: sl<FarmerRemoteDataSource>(),
      localDataSource: sl<FarmerLocalDataSource>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton<SMRReportRepository>(
    () => SMRReportRepositoryImpl(
      remoteDataSource: sl<SMRReportRemoteDataSource>(),
      localDataSource: sl<SMRReportLocalDataSource>(),
      networkInfo: sl<NetworkInfo>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton<SalesOrderRepository>(
    () => SalesOrderRepositoryImpl(
      remoteDataSource: sl<SalesOrderRemoteDataSource>(),
      localDataSource: sl<SalesOrderLocalDataSource>(),
      networkInfo: sl<NetworkInfo>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton<DuesRepository>(
    () => DuesRepositoryImpl(
      localDataSource: sl<DuesLocalDataSource>(),
      remoteDataSource: sl<DuesRemoteDataSource>(),
      networkInfo: sl<NetworkInfo>(),
    ),
  );

  sl.registerLazySingleton<InvoiceRepository>(
    () => InvoiceRepositoryImpl(
      localDataSource: sl<InvoiceLocalDataSource>(),
      remoteDataSource: sl<InvoiceRemoteDataSource>(),
      networkInfo: sl<NetworkInfo>(),
    ),
  );

  sl.registerLazySingleton<InvoiceStatusRepository>(
    () => InvoiceStatusRepositoryImpl(
      remoteDataSource: sl<InvoiceStatusRemoteDataSource>(),
    ),
  );

  sl.registerLazySingleton<CustomerPaymentsRepository>(
    () => CustomerPaymentsRepositoryImpl(
      remoteDataSource: sl<CustomerPaymentsRemoteDataSource>(),
      localDataSource: sl<CustomerPaymentsLocalDataSource>(),
      networkInfo: sl<NetworkInfo>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton<PaymentRepository>(
    () => PaymentRepositoryImpl(
      remoteDataSource: sl<PaymentRemoteDataSource>(),
      networkInfo: sl<NetworkInfo>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton<ZohoBooksPaymentRepository>(
    () => ZohoBooksPaymentRepositoryImpl(
      remoteDataSource: sl<ZohoBooksPaymentRemoteDataSource>(),
      networkInfo: sl<NetworkInfo>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton<CreditNotesRepository>(
    () => CreditNotesRepositoryImpl(
      remoteDataSource: sl<CreditNotesRemoteDataSource>(),
      localDataSource: sl<CreditNotesLocalDataSource>(),
      networkInfo: sl<NetworkInfo>(),
      logger: sl<AppLogger>(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(
      auth: sl<FirebaseAuth>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton<UserLocalDataSource>(
    () => UserLocalDataSourceImpl(userBox: sl(), logger: sl<AppLogger>()),
  );

  sl.registerLazySingleton<PriceListRemoteDataSource>(
    () => PriceListRemoteDataSourceImpl(connectionManager: sl(), logger: sl()),
  );
  sl.registerLazySingleton<PriceListLocalDataSource>(
    () => PriceListLocalDataSourceImpl(priceListBox: sl(), logger: sl()),
  );
  sl.registerLazySingleton<ProductCatalogueRemoteDataSource>(
    () => ProductCatalogueRemoteDataSourceImpl(
      connectionManager: sl(),
      logger: sl(),
    ),
  );
  sl.registerLazySingleton<ProductCatalogueLocalDataSource>(
    () => ProductCatalogueLocalDataSourceImpl(
      productCatalogueBox: sl<Box<ObjectBoxProductCatalogueModel>>(),
      logger: sl(),
    ),
  );
  sl.registerLazySingleton<InterestedProductLocalDataSource>(
    () => InterestedProductLocalDataSourceImpl(
      interestedProductBox: sl<Box<ObjectBoxInterestedProductModel>>(),
      logger: sl<AppLogger>(),
    ),
  );
  sl.registerLazySingleton<InterestedProductRemoteDataSource>(
    () => InterestedProductRemoteDataSourceImpl(
      connectionManager: sl(),
      logger: sl(),
    ),
  );
  sl.registerLazySingleton<DashboardRemoteDataSource>(
    () => DashboardRemoteDataSourceImpl(
      apiClient: sl<ApiClient>(),
      logger: sl<AppLogger>(),
    ),
  );
  sl.registerLazySingleton<CustomerSchemeLocalDataSource>(
    () => CustomerSchemeLocalDataSourceImpl(
      customerSchemeBox: sl<Box<CustomerSchemeModel>>(),
    ),
  );
  sl.registerLazySingleton<CustomerSchemeRemoteDataSource>(
    () => CustomerSchemeRemoteDataSourceImpl(
      apiClient: sl<ApiClient>(),
      logger: sl<AppLogger>(),
    ),
  );
  sl.registerLazySingleton<DashboardLocalDataSource>(
    () => DashboardLocalDataSourceImpl(
      dashboardBox: sl<Box<DashboardModel>>(),
      logger: sl<AppLogger>(),
    ),
  );
  sl.registerLazySingleton<CustomerLocalDataSource>(
    () => CustomerLocalDataSourceImpl(
      customerBox: sl<Box<ObjectBoxCustomerModel>>(),
      sharedPreferences: sl<SharedPreferences>(),
    ),
  );
  sl.registerLazySingleton<CustomerRemoteDataSource>(
    () => CustomerRemoteDataSourceImpl(
      apiClient: sl<ApiClient>(),
      logger: sl<AppLogger>(), // Add logger
    ),
  );
  sl.registerLazySingleton<UserRemoteDataSource>(
    () => UserRemoteDataSourceImpl(connectionManager: sl(), logger: sl()),
  );
  sl.registerLazySingleton<RemoteConfigDataSource>(
    () => FirebaseRemoteConfigDataSource(sl<FirebaseRemoteConfig>()),
  );
  sl.registerLazySingleton<DeviceInfoDataSource>(
    () => DeviceInfoDataSourceImpl(),
  );
  sl.registerLazySingleton<MongoDBDataSource>(
    () => MongoDBDataSourceImpl(sl<MongoDBService>()),
  );
  sl.registerLazySingleton(
    () => MongoDbConnectionManager(
      networkInfo: sl<NetworkInfo>(),
      logger: sl<AppLogger>(),
      mongoDBService: sl<MongoDBService>(),
    ),
  );
  sl.registerLazySingleton<AccountStatementRemoteDataSource>(
    () => AccountStatementRemoteDataSourceImpl(
      apiClient: sl<ApiClient>(),
      logger: sl<AppLogger>(),
    ),
  );
  sl.registerLazySingleton<AccountStatementLocalDataSource>(
    () => AccountStatementLocalDataSourceImpl(
      accountStatementBox: sl<Box<AccountStatementModel>>(),
      sharedPreferences: sl<SharedPreferences>(),
      logger: sl<AppLogger>(),
    ),
  );
  sl.registerLazySingleton<FarmerRemoteDataSource>(
    () => FarmerRemoteDataSourceImpl(
      apiClient: sl<ApiClient>(),
      logger: sl<AppLogger>(),
    ),
  );
  sl.registerLazySingleton<FarmerLocalDataSource>(
    () => FarmerLocalDataSourceImpl(
      store: sl<Store>(),
      farmerBox: sl<Box<FarmerEntity>>(),
      visitBox: sl<Box<VisitEntity>>(),
      logger: sl<AppLogger>(),
    ),
  );
  sl.registerLazySingleton<SMRReportRemoteDataSource>(
    () => SMRReportRemoteDataSourceImpl(
      apiClient: sl<ApiClient>(),
      logger: sl<AppLogger>(),
    ),
  );
  sl.registerLazySingleton<SMRReportLocalDataSource>(
    () => SMRReportLocalDataSourceImpl(
      smrReportBox: sl<Box<ObjectBoxSMRReportModel>>(),
      logger: sl<AppLogger>(),
    ),
  );
  sl.registerLazySingleton<SalesOrderRemoteDataSource>(
    () => SalesOrderRemoteDataSourceImpl(
      apiClient: sl<ApiClient>(),
      logger: sl<AppLogger>(),
    ),
  );
  sl.registerLazySingleton<SalesOrderLocalDataSource>(
    () => SalesOrderLocalDataSourceImpl(
      store: sl<Store>(),
      salesOrderBox: sl<Box<SalesOrderModel>>(),
      sharedPreferences: sl<SharedPreferences>(),
      logger: sl<AppLogger>(),
      salesOrderItemBox: sl<Box<SalesOrderItemModel>>(),
    ),
  );
  sl.registerLazySingleton<DuesLocalDataSource>(
    () => DuesLocalDataSourceImpl(
      invoiceBox: sl<Box<DuesInvoiceModel>>(),
      agingGroupBox: sl<Box<DuesAgingGroupModel>>(),
      summaryBox: sl<Box<DuesSummaryModel>>(),
      store: sl<Store>(),
    ),
  );
  sl.registerLazySingleton<DuesRemoteDataSource>(
    () => DuesRemoteDataSourceImpl(
      apiClient: sl<ApiClient>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton<InvoiceLocalDataSource>(
    () => InvoiceLocalDataSourceImpl(
      invoiceBox: sl<Box<ObjectBoxInvoiceModel>>(),
      sharedPreferences: sl<SharedPreferences>(),
    ),
  );
  sl.registerLazySingleton<InvoiceRemoteDataSource>(
    () => InvoiceRemoteDataSourceImpl(
      apiClient: sl<ApiClient>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton<InvoiceStatusRemoteDataSource>(
    () => InvoiceStatusRemoteDataSourceImpl(apiClient: sl<ApiClient>()),
  );
  sl.registerLazySingleton<CustomerPaymentsRemoteDataSource>(
    () => CustomerPaymentsRemoteDataSourceImpl(
      apiClient: sl<ApiClient>(),
      logger: sl<AppLogger>(),
    ),
  );
  sl.registerLazySingleton<PaymentRemoteDataSource>(
    () => PaymentRemoteDataSourceImpl(
      apiClient: sl<ApiClient>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton<ZohoBooksPaymentRemoteDataSource>(
    () => ZohoBooksPaymentRemoteDataSourceImpl(
      apiClient: sl<ApiClient>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton<CustomerPaymentsLocalDataSource>(
    () => CustomerPaymentsLocalDataSourceImpl(
      customerPaymentsBox: sl<Box<CustomerPaymentModel>>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton<CreditNotesRemoteDataSource>(
    () => CreditNotesRemoteDataSourceImpl(
      apiClient: sl<ApiClient>(),
      logger: sl<AppLogger>(),
    ),
  );
  sl.registerLazySingleton<CreditNotesLocalDataSource>(
    () => CreditNotesLocalDataSourceImpl(
      creditNoteBox: sl<Box<CreditNoteModel>>(),
      store: sl<Store>(),
    ),
  );
  // ObjectBox setup
  logger.i("Setting up ObjectBox");
  // ObjectBox setup with proper configuration
  logger.i("Initializing ObjectBox database");
  final objectbox = await ObjectBox.create();
  sl.registerLazySingleton(() => objectbox);
  final store = objectbox.store;
  sl.registerLazySingleton(() => store);
  sl.registerLazySingleton(() => store.box<UserModel>());
  sl.registerLazySingleton(() => store.box<ObjectBoxCustomerModel>());
  sl.registerLazySingleton(() => store.box<ObjectBoxPriceListModel>());
  sl.registerLazySingleton(() => store.box<ObjectBoxProductCatalogueModel>());
  sl.registerLazySingleton(() => store.box<ObjectBoxInterestedProductModel>());
  sl.registerLazySingleton(() => store.box<DashboardModel>());
  sl.registerLazySingleton(() => store.box<CustomerSchemeModel>());
  sl.registerLazySingleton(() => store.box<AccountStatementModel>());
  sl.registerLazySingleton(() => store.box<FarmerEntity>());
  sl.registerLazySingleton(() => store.box<VisitEntity>());
  sl.registerLazySingleton(() => store.box<Visibility>());
  sl.registerLazySingleton(() => store.box<ObjectBoxSMRReportModel>());
  sl.registerLazySingleton(() => store.box<SalesOrderModel>());
  sl.registerLazySingleton(() => store.box<SalesOrderItemModel>());
  sl.registerLazySingleton(() => store.box<DuesInvoiceModel>());
  sl.registerLazySingleton(() => store.box<DuesAgingGroupModel>());
  sl.registerLazySingleton(() => store.box<DuesSummaryModel>());
  sl.registerLazySingleton(() => store.box<ObjectBoxInvoiceModel>());
  sl.registerLazySingleton(() => store.box<CustomerPaymentModel>());
  sl.registerLazySingleton(() => store.box<CreditNoteModel>());

  logger.i("Dependencies initialized successfully");
}
