import 'dart:convert';
import 'package:aquapartner/data/models/payments/zoho_books_payment_session_model.dart';
import 'package:flutter_test/flutter_test.dart';

/// Test to verify response parsing logic for different API response structures
void main() {
  group('Zoho Books Response Parsing Tests', () {
    test('should parse standard response structure', () {
      final responseJson = {
        'payment_link_url': 'https://books.zoho.in/checkout/test_token_123',
        'status': 'success',
      };

      final sessionData = {
        'invoice_id': 'ADJ-DR/CR-INV43',
        'payment_link_url': responseJson['payment_link_url'],
        'amount': 100.0,
        'currency': 'INR',
        'description': 'Test payment',
        'created_at': DateTime.now().toIso8601String(),
      };

      final session = ZohoBooksPaymentSessionModel.fromJson(sessionData);
      
      expect(session.paymentLinkUrl, equals('https://books.zoho.in/checkout/test_token_123'));
      expect(session.invoiceId, equals('ADJ-DR/CR-INV43'));
      
      print('✅ Standard response structure parsed successfully');
    });

    test('should handle missing payment_link_url field', () {
      final responseJson = {
        'status': 'success',
        'message': 'Payment link created',
        // Missing payment_link_url field
      };

      print('Testing response without payment_link_url: $responseJson');
      print('Available fields: ${responseJson.keys.toList()}');
      
      // This should fail in our enhanced parsing logic
      expect(responseJson.containsKey('payment_link_url'), isFalse);
      print('❌ Missing payment_link_url field detected');
    });

    test('should handle nested response structure', () {
      final responseJson = {
        'status': 'success',
        'data': {
          'payment_link_url': 'https://books.zoho.in/checkout/nested_token_123',
          'invoice_id': 'ADJ-DR/CR-INV43',
        },
      };

      print('Testing nested response structure: $responseJson');
      
      // Test our nested structure parsing logic
      String? paymentLinkUrl;
      if (responseJson.containsKey('data') && responseJson['data'] is Map) {
        final dataMap = responseJson['data'] as Map<String, dynamic>;
        if (dataMap.containsKey('payment_link_url')) {
          paymentLinkUrl = dataMap['payment_link_url'].toString();
        }
      }

      expect(paymentLinkUrl, equals('https://books.zoho.in/checkout/nested_token_123'));
      print('✅ Nested response structure parsed successfully');
    });

    test('should handle alternative field names', () {
      final testCases = [
        {
          'name': 'paymentLinkUrl (camelCase)',
          'response': {'paymentLinkUrl': 'https://books.zoho.in/checkout/camel_case'},
        },
        {
          'name': 'payment_link (underscore)',
          'response': {'payment_link': 'https://books.zoho.in/checkout/underscore'},
        },
        {
          'name': 'paymentUrl (short)',
          'response': {'paymentUrl': 'https://books.zoho.in/checkout/short'},
        },
      ];

      for (final testCase in testCases) {
        final response = testCase['response'] as Map<String, dynamic>;
        print('Testing ${testCase['name']}: $response');
        
        // Our parsing logic should handle these variations
        String? paymentLinkUrl;
        
        if (response.containsKey('payment_link_url')) {
          paymentLinkUrl = response['payment_link_url'].toString();
        } else if (response.containsKey('paymentLinkUrl')) {
          paymentLinkUrl = response['paymentLinkUrl'].toString();
        } else if (response.containsKey('payment_link')) {
          paymentLinkUrl = response['payment_link'].toString();
        }

        expect(paymentLinkUrl, isNotNull);
        print('✅ ${testCase['name']} parsed successfully: $paymentLinkUrl');
      }
    });

    test('should identify problematic response structures', () {
      final problematicResponses = [
        {
          'name': 'Empty response',
          'response': {},
        },
        {
          'name': 'Null payment URL',
          'response': {'payment_link_url': null},
        },
        {
          'name': 'Empty payment URL',
          'response': {'payment_link_url': ''},
        },
        {
          'name': 'Wrong field name',
          'response': {'payment_url': 'https://example.com'},
        },
        {
          'name': 'Error response',
          'response': {'error': 'Invalid invoice ID', 'status': 'failed'},
        },
      ];

      for (final testCase in problematicResponses) {
        final response = testCase['response'] as Map<String, dynamic>;
        print('\n--- Testing ${testCase['name']} ---');
        print('Response: $response');
        print('Available fields: ${response.keys.toList()}');
        
        // Check if our parsing would succeed
        bool hasValidPaymentUrl = false;
        String? paymentLinkUrl;
        
        if (response.containsKey('payment_link_url') && response['payment_link_url'] != null) {
          paymentLinkUrl = response['payment_link_url'].toString();
          if (paymentLinkUrl.isNotEmpty) {
            hasValidPaymentUrl = true;
          }
        }

        print('Has valid payment URL: $hasValidPaymentUrl');
        if (!hasValidPaymentUrl) {
          print('❌ This response would cause a ServerFailure');
        } else {
          print('✅ This response would be parsed successfully');
        }
      }
    });

    test('should simulate ADJ-DR/CR-INV43 invoice response', () {
      // Simulate what the API might return for adjustment/credit invoices
      final possibleResponses = [
        {
          'name': 'Standard success response',
          'response': {
            'payment_link_url': 'https://books.zoho.in/checkout/adj_token_123',
            'status': 'success',
            'invoice_id': 'ADJ-DR/CR-INV43',
          },
        },
        {
          'name': 'Error response for adjustment invoice',
          'response': {
            'error': 'Payment links not supported for adjustment invoices',
            'status': 'failed',
            'invoice_type': 'adjustment',
          },
        },
        {
          'name': 'Nested response structure',
          'response': {
            'status': 'success',
            'data': {
              'payment_link_url': 'https://books.zoho.in/checkout/adj_nested_123',
              'invoice_type': 'adjustment',
            },
          },
        },
      ];

      for (final testCase in possibleResponses) {
        final response = testCase['response'] as Map<String, dynamic>;
        print('\n=== ${testCase['name']} ===');
        print('Response: $response');
        
        // Simulate our parsing logic
        String? paymentLinkUrl;
        bool isError = false;
        
        // Check for error first
        if (response.containsKey('error')) {
          isError = true;
          print('❌ Error response: ${response['error']}');
        } else {
          // Try to extract payment URL
          if (response.containsKey('payment_link_url') && response['payment_link_url'] != null) {
            paymentLinkUrl = response['payment_link_url'].toString();
          } else if (response.containsKey('data') && response['data'] is Map) {
            final dataMap = response['data'] as Map<String, dynamic>;
            if (dataMap.containsKey('payment_link_url')) {
              paymentLinkUrl = dataMap['payment_link_url'].toString();
            }
          }
          
          if (paymentLinkUrl != null && paymentLinkUrl.isNotEmpty) {
            print('✅ Payment URL extracted: $paymentLinkUrl');
          } else {
            print('❌ No valid payment URL found');
          }
        }
      }
    });
  });
}
