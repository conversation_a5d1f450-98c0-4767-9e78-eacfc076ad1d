// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in aquapartner/test/data/repositories/invoice_status_repository_impl_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:aquapartner/core/utils/logger.dart' as _i6;
import 'package:aquapartner/data/datasources/remote/invoice_status_remote_data_source.dart'
    as _i3;
import 'package:aquapartner/data/models/invoices/invoice_status_update_request_model.dart'
    as _i5;
import 'package:aquapartner/data/models/invoices/invoice_status_update_response_model.dart'
    as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeInvoiceStatusUpdateResponseModel_0 extends _i1.SmartFake
    implements _i2.InvoiceStatusUpdateResponseModel {
  _FakeInvoiceStatusUpdateResponseModel_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

/// A class which mocks [InvoiceStatusRemoteDataSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockInvoiceStatusRemoteDataSource extends _i1.Mock
    implements _i3.InvoiceStatusRemoteDataSource {
  MockInvoiceStatusRemoteDataSource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.InvoiceStatusUpdateResponseModel> updateInvoiceStatus(
    _i5.InvoiceStatusUpdateRequestModel? request,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateInvoiceStatus, [request]),
            returnValue: _i4.Future<_i2.InvoiceStatusUpdateResponseModel>.value(
              _FakeInvoiceStatusUpdateResponseModel_0(
                this,
                Invocation.method(#updateInvoiceStatus, [request]),
              ),
            ),
          )
          as _i4.Future<_i2.InvoiceStatusUpdateResponseModel>);
}

/// A class which mocks [AppLogger].
///
/// See the documentation for Mockito's code generation for more information.
class MockAppLogger extends _i1.Mock implements _i6.AppLogger {
  MockAppLogger() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void d(String? message) => super.noSuchMethod(
    Invocation.method(#d, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void i(String? message) => super.noSuchMethod(
    Invocation.method(#i, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void w(String? message) => super.noSuchMethod(
    Invocation.method(#w, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void e(String? message, [dynamic error, StackTrace? stackTrace]) =>
      super.noSuchMethod(
        Invocation.method(#e, [message, error, stackTrace]),
        returnValueForMissingStub: null,
      );

  @override
  void enableFirebaseVerboseLogging() => super.noSuchMethod(
    Invocation.method(#enableFirebaseVerboseLogging, []),
    returnValueForMissingStub: null,
  );
}
