import 'package:aqua_ui/aqua_ui.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../../../core/mixins/analytics_mixin.dart';
import '../../../core/utils/logger.dart';
import '../../../domain/entities/payments/payment_session.dart';

/// Callback function for payment completion
typedef PaymentCompletionCallback =
    void Function({
      required bool isSuccess,
      String? transactionId,
      String? errorMessage,
    });

/// WebView widget for Zoho payment integration using hosted checkout page
///
/// This implementation loads our hosted checkout page at:
/// https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/checkout
///
/// It leverages our proven Next.js Zoho payment integration and detects
/// payment completion by monitoring navigation to our hosted domain's
/// success/failure/cancellation URLs.
class ZohoPaymentWebView extends StatefulWidget {
  final PaymentSession paymentSession;
  final PaymentCompletionCallback onPaymentComplete;
  final VoidCallback? onCancel;
  final String? customerName;
  final String? customerEmail;
  final String? invoiceNumber;

  const ZohoPaymentWebView({
    super.key,
    required this.paymentSession,
    required this.onPaymentComplete,
    this.onCancel,
    this.customerName,
    this.customerEmail,
    this.invoiceNumber,
  });

  @override
  State<ZohoPaymentWebView> createState() => _ZohoPaymentWebViewState();
}

class _ZohoPaymentWebViewState extends State<ZohoPaymentWebView>
    with AnalyticsMixin {
  late final WebViewController _controller;
  bool _isLoading = true;
  String? _errorMessage;
  final AppLogger _logger = AppLogger();

  @override
  String get screenName => 'ZohoPaymentWebView';

  @override
  void initState() {
    super.initState();
    _initializeWebView();

    // Track payment webview initialization
    trackEvent(
      'payment_webview_initialized',
      params: {
        'session_id': widget.paymentSession.sessionId,
        'amount': widget.paymentSession.amount.toString(),
        'currency': widget.paymentSession.currency,
      },
    );
  }

  void _initializeWebView() {
    _controller =
        WebViewController()
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setNavigationDelegate(
            NavigationDelegate(
              onPageStarted: (String url) {
                _logger.i('Payment page started loading: $url');
                setState(() {
                  _isLoading = true;
                  _errorMessage = null;
                });
              },
              onPageFinished: (String url) {
                _logger.i('Payment page finished loading: $url');
                setState(() {
                  _isLoading = false;
                });

                // Track page load completion
                trackEvent(
                  'payment_webview_page_loaded',
                  params: {
                    'url': url,
                    'session_id': widget.paymentSession.sessionId,
                  },
                );
              },
              onWebResourceError: (WebResourceError error) {
                _logger.e('WebView error: ${error.description}');
                setState(() {
                  _isLoading = false;
                  _errorMessage =
                      'Failed to load payment page: ${error.description}';
                });

                // Track webview error
                trackEvent(
                  'payment_webview_error',
                  params: {
                    'error_code': error.errorCode.toString(),
                    'error_description': error.description,
                    'session_id': widget.paymentSession.sessionId,
                  },
                );
              },
              onNavigationRequest: (NavigationRequest request) {
                _logger.i('Navigation request: ${request.url}');

                // Handle hosted payment completion URLs
                if (_isPaymentCompletionUrl(request.url)) {
                  _handlePaymentCompletionUrl(request.url);
                  return NavigationDecision.prevent;
                }

                // Allow all other navigation
                return NavigationDecision.navigate;
              },
            ),
          );

    // Load the hosted checkout page
    _loadHostedCheckout();
  }

  bool _isPaymentCompletionUrl(String url) {
    // Check for Zoho payment completion URLs
    return url.contains('payments.zoho.in') &&
        (url.contains('/success') ||
            url.contains('/failure') ||
            url.contains('/cancel') ||
            url.contains('payment_status=') ||
            url.contains('status=') ||
            url.contains('success=') ||
            url.contains('error=') ||
            url.contains('cancelled=') ||
            // Also check for our hosted domain completion URLs as fallback
            (url.contains(
                  'aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net',
                ) &&
                (url.contains('/payment-success') ||
                    url.contains('/payment-failure') ||
                    url.contains('/payment-cancel'))));
  }

  void _handlePaymentCompletionUrl(String url) {
    _logger.i('Payment completion URL detected: $url');

    final uri = Uri.parse(url);
    final queryParams = uri.queryParameters;

    // Check for success indicators from both Zoho and hosted checkout
    bool isSuccess =
        url.contains('/success') ||
        url.contains('/payment-success') ||
        queryParams['success'] == 'true' ||
        queryParams['payment_status'] == 'success' ||
        queryParams['status'] == 'success' ||
        queryParams['payment_status'] == 'completed';

    // Check for cancellation indicators from both Zoho and hosted checkout
    bool isCancelled =
        url.contains('/cancel') ||
        url.contains('/payment-cancel') ||
        queryParams['cancelled'] == 'true' ||
        queryParams['payment_status'] == 'cancelled' ||
        queryParams['status'] == 'cancelled';

    // Extract transaction ID from various possible parameter names
    String? transactionId =
        queryParams['payment_id'] ??
        queryParams['transaction_id'] ??
        queryParams['txn_id'] ??
        queryParams['zpay_payment_id'] ??
        queryParams['transactionId'] ??
        queryParams['payment_link_id'];

    if (isSuccess) {
      _logger.i('Payment successful with transaction ID: $transactionId');

      // Track payment success
      trackEvent(
        'payment_webview_success',
        params: {
          'transaction_id': transactionId ?? 'unknown',
          'session_id': widget.paymentSession.sessionId,
          'completion_url': url,
          'payment_method':
              url.contains('payments.zoho.in') ? 'zoho_direct' : 'hosted',
        },
      );

      widget.onPaymentComplete(isSuccess: true, transactionId: transactionId);
    } else if (isCancelled) {
      _logger.i('Payment cancelled by user');

      // Track payment cancellation
      trackEvent(
        'payment_webview_cancelled',
        params: {
          'session_id': widget.paymentSession.sessionId,
          'completion_url': url,
          'payment_method':
              url.contains('payments.zoho.in') ? 'zoho_direct' : 'hosted',
        },
      );

      widget.onPaymentComplete(
        isSuccess: false,
        errorMessage: 'Payment was cancelled by user',
      );
    } else {
      // Assume failure for any other completion URL
      String errorMessage =
          queryParams['error'] ??
          queryParams['error_message'] ??
          queryParams['message'] ??
          'Payment failed';

      _logger.e('Payment failed: $errorMessage');

      // Track payment failure
      trackEvent(
        'payment_webview_failure',
        params: {
          'error_message': errorMessage,
          'session_id': widget.paymentSession.sessionId,
          'completion_url': url,
          'payment_method':
              url.contains('payments.zoho.in') ? 'zoho_direct' : 'hosted',
        },
      );

      widget.onPaymentComplete(isSuccess: false, errorMessage: errorMessage);
    }
  }

  void _loadHostedCheckout() async {
    try {
      // Use the payment URL directly from the API response
      final paymentUrl = widget.paymentSession.sessionUrl;

      if (paymentUrl.isEmpty) {
        throw Exception('Payment URL is empty');
      }

      _logger.i('Loading Zoho payment URL: $paymentUrl');

      await _controller.loadRequest(Uri.parse(paymentUrl));
    } catch (e) {
      _logger.e('Error loading payment URL: $e');
      setState(() {
        _errorMessage = 'Failed to load payment interface';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: AquaText.subheadline(
          'Payment',
          color: Colors.white,
          weight: AquaFontWeight.semibold,
        ),
        backgroundColor: acPrimaryBlue,
        iconTheme: const IconThemeData(color: Colors.white),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            // Track manual cancellation
            trackEvent(
              'payment_webview_manual_cancel',
              params: {'session_id': widget.paymentSession.sessionId},
            );

            if (widget.onCancel != null) {
              widget.onCancel!();
            } else {
              Navigator.of(context).pop();
            }
          },
        ),
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_errorMessage != null) {
      return _buildErrorView();
    }

    return Stack(
      children: [
        WebViewWidget(controller: _controller),
        if (_isLoading) _buildLoadingOverlay(),
      ],
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            AquaText.subheadline(
              'Payment Error',
              weight: AquaFontWeight.semibold,
              color: acRedColor,
            ),
            const SizedBox(height: 8),
            AquaText.body(
              _errorMessage!,
              textAlign: TextAlign.center,
              color: acGrey300,
            ),
            const SizedBox(height: 24),
            AquaButton(
              title: 'Retry',
              onTap: () {
                setState(() {
                  _errorMessage = null;
                  _isLoading = true;
                });
                _controller.reload();
              },
              color: acWhiteColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    return Container(
      color: Colors.white.withValues(alpha: 0.8),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'Loading payment page...',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}
