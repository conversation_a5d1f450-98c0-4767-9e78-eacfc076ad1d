# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\dev\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\flutter_projects\\aquapartner" PROJECT_DIR)

set(FLUTTER_VERSION "1.1.3" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 3 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 0 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\dev\\flutter"
  "PROJECT_DIR=D:\\flutter_projects\\aquapartner"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\dev\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\flutter_projects\\aquapartner\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\flutter_projects\\aquapartner"
  "FLUTTER_TARGET=D:\\flutter_projects\\aquapartner\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\flutter_projects\\aquapartner\\.dart_tool\\package_config.json"
)
