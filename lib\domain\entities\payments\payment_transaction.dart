import 'package:equatable/equatable.dart';

/// Domain entity representing a payment transaction result
class PaymentTransaction extends Equatable {
  final String transactionId;
  final String paymentId;
  final String sessionId;
  final String invoiceNumber;
  final double amount;
  final String currency;
  final PaymentStatus status;
  final String? errorMessage;
  final DateTime timestamp;
  final Map<String, dynamic>? additionalData;

  const PaymentTransaction({
    required this.transactionId,
    required this.paymentId,
    required this.sessionId,
    required this.invoiceNumber,
    required this.amount,
    required this.currency,
    required this.status,
    this.errorMessage,
    required this.timestamp,
    this.additionalData,
  });

  @override
  List<Object?> get props => [
        transactionId,
        paymentId,
        sessionId,
        invoiceNumber,
        amount,
        currency,
        status,
        errorMessage,
        timestamp,
        additionalData,
      ];
}

/// Enum representing payment transaction status
enum PaymentStatus {
  pending,
  success,
  failed,
  cancelled,
  timeout,
}

/// Extension to convert PaymentStatus to string
extension PaymentStatusExtension on PaymentStatus {
  String get value {
    switch (this) {
      case PaymentStatus.pending:
        return 'pending';
      case PaymentStatus.success:
        return 'success';
      case PaymentStatus.failed:
        return 'failed';
      case PaymentStatus.cancelled:
        return 'cancelled';
      case PaymentStatus.timeout:
        return 'timeout';
    }
  }

  static PaymentStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return PaymentStatus.pending;
      case 'success':
        return PaymentStatus.success;
      case 'failed':
        return PaymentStatus.failed;
      case 'cancelled':
        return PaymentStatus.cancelled;
      case 'timeout':
        return PaymentStatus.timeout;
      default:
        return PaymentStatus.failed;
    }
  }
}
