import 'package:aquapartner/core/error/exceptions.dart';
import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/data/datasources/remote/invoice_status_remote_data_source.dart';
import 'package:aquapartner/data/models/invoices/invoice_status_update_request_model.dart';
import 'package:aquapartner/data/models/invoices/invoice_status_update_response_model.dart';
import 'package:aquapartner/data/repositories/invoice_status_repository_impl.dart';
import 'package:aquapartner/domain/entities/invoices/invoice_status_update_request.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'invoice_status_repository_impl_test.mocks.dart';

@GenerateMocks([InvoiceStatusRemoteDataSource, AppLogger])
void main() {
  late InvoiceStatusRepositoryImpl repository;
  late MockInvoiceStatusRemoteDataSource mockRemoteDataSource;
  late MockAppLogger mockLogger;

  setUp(() {
    mockRemoteDataSource = MockInvoiceStatusRemoteDataSource();
    mockLogger = MockAppLogger();
    repository = InvoiceStatusRepositoryImpl(
      remoteDataSource: mockRemoteDataSource,
      logger: mockLogger,
    );
  });

  group('InvoiceStatusRepositoryImpl', () {
    const tInvoiceId = '401088000082205103';
    const tPaymentAmount = 3.00;
    const tTransactionId = 'TXN-FLUTTER-123';
    final tPaymentDate = DateTime.parse('2025-07-10T10:30:00.000Z');

    final tRequest = InvoiceStatusUpdateRequest.fromPaymentSuccess(
      invoiceId: tInvoiceId,
      paymentAmount: tPaymentAmount,
      transactionId: tTransactionId,
      paymentDate: tPaymentDate,
    );

    final tResponseModel = InvoiceStatusUpdateResponseModel(
      success: true,
      message: 'Invoice status updated successfully',
      data: InvoiceStatusUpdateDataModel(
        invoiceId: tInvoiceId,
        previousStatus: 'Overdue',
        currentStatus: 'Paid',
        paymentCompletedAt: tPaymentDate.toIso8601String(),
        updatedAt: DateTime.now().toIso8601String(),
      ),
    );

    test(
      'should return invoice status update response when remote call is successful',
      () async {
        // arrange
        when(
          mockRemoteDataSource.updateInvoiceStatus(any),
        ).thenAnswer((_) async => tResponseModel);

        // act
        final result = await repository.updateInvoiceStatus(tRequest);

        // assert
        expect(result, isA<Right>());
        result.fold((failure) => fail('Expected Right but got Left'), (
          response,
        ) {
          expect(response.success, isTrue);
          expect(response.invoiceId, equals(tInvoiceId));
          expect(response.newStatus, equals('Paid'));
          expect(response.previousStatus, equals('Overdue'));
        });
        verify(mockRemoteDataSource.updateInvoiceStatus(any));
        verifyNoMoreInteractions(mockRemoteDataSource);
      },
    );

    test(
      'should return ServerFailure when remote data source throws ServerException',
      () async {
        // arrange
        when(
          mockRemoteDataSource.updateInvoiceStatus(any),
        ).thenThrow(ServerException());

        // act
        final result = await repository.updateInvoiceStatus(tRequest);

        // assert
        expect(result, Left(ServerFailure()));
        verify(mockRemoteDataSource.updateInvoiceStatus(any));
        verifyNoMoreInteractions(mockRemoteDataSource);
      },
    );

    test(
      'should return NetworkFailure when remote data source throws NetworkException',
      () async {
        // arrange
        when(
          mockRemoteDataSource.updateInvoiceStatus(any),
        ).thenThrow(NetworkException());

        // act
        final result = await repository.updateInvoiceStatus(tRequest);

        // assert
        expect(result, Left(NetworkFailure()));
        verify(mockRemoteDataSource.updateInvoiceStatus(any));
        verifyNoMoreInteractions(mockRemoteDataSource);
      },
    );

    test(
      'should return AuthFailure when remote data source throws AuthException',
      () async {
        // arrange
        when(
          mockRemoteDataSource.updateInvoiceStatus(any),
        ).thenThrow(AuthException());

        // act
        final result = await repository.updateInvoiceStatus(tRequest);

        // assert
        expect(result, Left(AuthFailure()));
        verify(mockRemoteDataSource.updateInvoiceStatus(any));
        verifyNoMoreInteractions(mockRemoteDataSource);
      },
    );

    test(
      'should return ServerFailure when remote data source throws unexpected exception',
      () async {
        // arrange
        when(
          mockRemoteDataSource.updateInvoiceStatus(any),
        ).thenThrow(Exception('Unexpected error'));

        // act
        final result = await repository.updateInvoiceStatus(tRequest);

        // assert
        expect(result, Left(ServerFailure()));
        verify(mockRemoteDataSource.updateInvoiceStatus(any));
        verifyNoMoreInteractions(mockRemoteDataSource);
      },
    );

    test('should pass correct request model to remote data source', () async {
      // arrange
      when(
        mockRemoteDataSource.updateInvoiceStatus(any),
      ).thenAnswer((_) async => tResponseModel);

      // act
      await repository.updateInvoiceStatus(tRequest);

      // assert
      final captured =
          verify(mockRemoteDataSource.updateInvoiceStatus(captureAny)).captured;
      final capturedModel = captured.first as InvoiceStatusUpdateRequestModel;

      expect(capturedModel.invoiceId, equals(tInvoiceId));
      expect(
        capturedModel.paymentDetails.paymentAmount,
        equals(tPaymentAmount),
      );
      expect(
        capturedModel.paymentDetails.transactionId,
        equals(tTransactionId),
      );
      expect(
        capturedModel.paymentDetails.paymentMethod,
        equals('zoho_books_payment_link'),
      );
    });

    test('should handle response with different status values', () async {
      // arrange
      final differentStatusResponse = InvoiceStatusUpdateResponseModel(
        success: true,
        message: 'Status updated',
        data: InvoiceStatusUpdateDataModel(
          invoiceId: tInvoiceId,
          previousStatus: 'Pending',
          currentStatus: 'Completed',
          paymentCompletedAt: tPaymentDate.toIso8601String(),
          updatedAt: DateTime.now().toIso8601String(),
        ),
      );

      when(
        mockRemoteDataSource.updateInvoiceStatus(any),
      ).thenAnswer((_) async => differentStatusResponse);

      // act
      final result = await repository.updateInvoiceStatus(tRequest);

      // assert
      expect(result, isA<Right>());
      result.fold((failure) => fail('Expected Right but got Left'), (response) {
        expect(response.success, isTrue);
        expect(response.previousStatus, equals('Pending'));
        expect(response.newStatus, equals('Completed'));
        expect(response.isStatusUpdatedToPaid, isFalse); // Not 'Paid'
      });
    });

    test(
      'should handle response indicating payment status update to Paid',
      () async {
        // arrange
        when(
          mockRemoteDataSource.updateInvoiceStatus(any),
        ).thenAnswer((_) async => tResponseModel);

        // act
        final result = await repository.updateInvoiceStatus(tRequest);

        // assert
        expect(result, isA<Right>());
        result.fold((failure) => fail('Expected Right but got Left'), (
          response,
        ) {
          expect(response.isStatusUpdatedToPaid, isTrue);
          expect(response.newStatus.toLowerCase(), equals('paid'));
        });
      },
    );
  });
}
