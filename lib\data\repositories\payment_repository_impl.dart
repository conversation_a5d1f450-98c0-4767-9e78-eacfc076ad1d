import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../core/network/network_info.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/payments/payment_request.dart';
import '../../domain/entities/payments/payment_session.dart';
import '../../domain/entities/payments/payment_transaction.dart';
import '../../domain/repositories/payment_repository.dart';
import '../datasources/remote/payment_remote_data_source.dart';
import '../models/payments/payment_request_model.dart';

/// Implementation of payment repository
class PaymentRepositoryImpl implements PaymentRepository {
  final PaymentRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;
  final AppLogger logger;

  PaymentRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
    required this.logger,
  });

  @override
  Future<Either<Failure, PaymentSession>> createPaymentSession(
    PaymentRequest request,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final requestModel = PaymentRequestModel.fromEntity(request);
        final sessionModel = await remoteDataSource.createPaymentSession(requestModel);
        return Right(sessionModel.toEntity());
      } on DioException catch (e) {
        logger.e('DioException in createPaymentSession: ${e.message}');
        return Left(ServerFailure());
      } on ServerException {
        logger.e('ServerException in createPaymentSession');
        return Left(ServerFailure());
      } on AuthException {
        logger.e('AuthException in createPaymentSession');
        return Left(AuthFailure());
      } catch (e) {
        logger.e('Unexpected error in createPaymentSession: ${e.toString()}');
        return Left(ServerFailure());
      }
    } else {
      logger.w('No network connection for createPaymentSession');
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, PaymentTransaction>> processPayment({
    required String sessionId,
    required String invoiceNumber,
    required double amount,
    required String currency,
  }) async {
    // For now, we'll create a mock transaction since the actual processing
    // happens in the WebView. This can be enhanced later with actual API calls.
    try {
      final transaction = PaymentTransaction(
        transactionId: 'TXN_${DateTime.now().millisecondsSinceEpoch}',
        paymentId: 'PAY_${DateTime.now().millisecondsSinceEpoch}',
        sessionId: sessionId,
        invoiceNumber: invoiceNumber,
        amount: amount,
        currency: currency,
        status: PaymentStatus.pending,
        timestamp: DateTime.now(),
      );
      
      return Right(transaction);
    } catch (e) {
      logger.e('Error in processPayment: ${e.toString()}');
      return Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, PaymentTransaction>> getPaymentStatus(
    String transactionId,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final transactionModel = await remoteDataSource.getPaymentStatus(transactionId);
        return Right(transactionModel.toEntity());
      } on DioException catch (e) {
        logger.e('DioException in getPaymentStatus: ${e.message}');
        return Left(ServerFailure());
      } on ServerException {
        logger.e('ServerException in getPaymentStatus');
        return Left(ServerFailure());
      } on AuthException {
        logger.e('AuthException in getPaymentStatus');
        return Left(AuthFailure());
      } catch (e) {
        logger.e('Unexpected error in getPaymentStatus: ${e.toString()}');
        return Left(ServerFailure());
      }
    } else {
      logger.w('No network connection for getPaymentStatus');
      return Left(NetworkFailure());
    }
  }
}
