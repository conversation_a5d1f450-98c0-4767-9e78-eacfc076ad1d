import 'package:aquapartner/data/models/payments/payment_session_model.dart';
import 'package:aquapartner/domain/entities/payments/payment_session.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('PaymentSessionModel', () {
    late Map<String, dynamic> testZohoLinkResponse;
    late Map<String, dynamic> testLegacyResponse;

    setUp(() {
      testZohoLinkResponse = {
        'success': true,
        'message': 'Payment link created successfully',
        'data': {
          'payment_link_id': '5619000000248086',
          'amount': '1500.50',
          'currency': 'INR',
          'description': 'Payment for Order #12345',
          'status': 'active',
          'created_time': 1751043827,
          'expires_at': '2025-07-27',
          'transaction_id': '685ecef3e19640cfcac11ebf'
        },
        'payment_link': {
          'url': 'https://payments.zoho.in/paymentlinks/test_token',
          'expires_at': '2025-07-27',
          'amount': '1500.50',
          'currency': 'INR',
          'status': 'active'
        }
      };

      testLegacyResponse = {
        'data': {
          'payment_session_id': 'session_123',
          'amount': 1500.50,
          'currency': 'INR',
          'created_time': 1751043827,
        },
        'payment_session': {
          'payments_session_id': 'session_123',
          'amount': 1500.50,
          'currency': 'INR',
          'meta_data': [
            {'key': 'invoice_number', 'value': 'INV001'},
            {'key': 'customer_id', 'value': 'customer_123'},
          ],
        }
      };
    });

    group('fromZohoLinkResponse', () {
      test('should create PaymentSessionModel from valid Zoho link response', () {
        // Act
        final result = PaymentSessionModel.fromZohoLinkResponse(testZohoLinkResponse);

        // Assert
        expect(result.sessionId, equals('5619000000248086'));
        expect(result.sessionUrl, equals('https://payments.zoho.in/paymentlinks/test_token'));
        expect(result.paymentSessionId, equals('5619000000248086'));
        expect(result.currency, equals('INR'));
        expect(result.amount, equals(1500.50));
        expect(result.createdTime, equals(1751043827));
        expect(result.metaData, isEmpty);
      });

      test('should handle missing payment_link_id by using transaction_id', () {
        // Arrange
        final responseWithoutLinkId = Map<String, dynamic>.from(testZohoLinkResponse);
        responseWithoutLinkId['data'].remove('payment_link_id');

        // Act
        final result = PaymentSessionModel.fromZohoLinkResponse(responseWithoutLinkId);

        // Assert
        expect(result.sessionId, equals('685ecef3e19640cfcac11ebf'));
        expect(result.paymentSessionId, equals('685ecef3e19640cfcac11ebf'));
      });

      test('should handle missing both payment_link_id and transaction_id', () {
        // Arrange
        final responseWithoutIds = Map<String, dynamic>.from(testZohoLinkResponse);
        responseWithoutIds['data'].remove('payment_link_id');
        responseWithoutIds['data'].remove('transaction_id');

        // Act
        final result = PaymentSessionModel.fromZohoLinkResponse(responseWithoutIds);

        // Assert
        expect(result.sessionId, isNotEmpty);
        expect(result.paymentSessionId, isNotEmpty);
        // Should generate a timestamp-based ID
        expect(int.tryParse(result.sessionId), isNotNull);
      });

      test('should handle missing payment_link URL', () {
        // Arrange
        final responseWithoutUrl = Map<String, dynamic>.from(testZohoLinkResponse);
        responseWithoutUrl['payment_link'].remove('url');

        // Act
        final result = PaymentSessionModel.fromZohoLinkResponse(responseWithoutUrl);

        // Assert
        expect(result.sessionUrl, equals(''));
      });

      test('should handle missing currency with default INR', () {
        // Arrange
        final responseWithoutCurrency = Map<String, dynamic>.from(testZohoLinkResponse);
        responseWithoutCurrency['data'].remove('currency');
        responseWithoutCurrency['payment_link'].remove('currency');

        // Act
        final result = PaymentSessionModel.fromZohoLinkResponse(responseWithoutCurrency);

        // Assert
        expect(result.currency, equals('INR'));
      });

      test('should handle amount from payment_link when data amount is missing', () {
        // Arrange
        final responseWithoutDataAmount = Map<String, dynamic>.from(testZohoLinkResponse);
        responseWithoutDataAmount['data'].remove('amount');

        // Act
        final result = PaymentSessionModel.fromZohoLinkResponse(responseWithoutDataAmount);

        // Assert
        expect(result.amount, equals(1500.50));
      });

      test('should handle missing created_time with default 0', () {
        // Arrange
        final responseWithoutCreatedTime = Map<String, dynamic>.from(testZohoLinkResponse);
        responseWithoutCreatedTime['data'].remove('created_time');

        // Act
        final result = PaymentSessionModel.fromZohoLinkResponse(responseWithoutCreatedTime);

        // Assert
        expect(result.createdTime, equals(0));
      });

      test('should handle empty data and payment_link sections', () {
        // Arrange
        final emptyResponse = {
          'success': true,
          'message': 'Payment link created successfully',
          'data': <String, dynamic>{},
          'payment_link': <String, dynamic>{},
        };

        // Act
        final result = PaymentSessionModel.fromZohoLinkResponse(emptyResponse);

        // Assert
        expect(result.sessionId, isNotEmpty);
        expect(result.sessionUrl, equals(''));
        expect(result.currency, equals('INR'));
        expect(result.amount, equals(0.0));
        expect(result.createdTime, equals(0));
      });
    });

    group('fromJson (legacy)', () {
      test('should create PaymentSessionModel from legacy response format', () {
        // Act
        final result = PaymentSessionModel.fromJson(testLegacyResponse);

        // Assert
        expect(result.sessionId, equals('session_123'));
        expect(result.paymentSessionId, equals('session_123'));
        expect(result.currency, equals('INR'));
        expect(result.amount, equals(1500.50));
        expect(result.createdTime, equals(1751043827));
        expect(result.metaData, hasLength(2));
        expect(result.metaData[0].key, equals('invoice_number'));
        expect(result.metaData[0].value, equals('INV001'));
      });
    });

    group('_parseAmount', () {
      test('should parse double amount correctly', () {
        expect(PaymentSessionModel.fromZohoLinkResponse({
          'data': {'amount': 1500.50},
          'payment_link': {},
        }).amount, equals(1500.50));
      });

      test('should parse int amount correctly', () {
        expect(PaymentSessionModel.fromZohoLinkResponse({
          'data': {'amount': 1500},
          'payment_link': {},
        }).amount, equals(1500.0));
      });

      test('should parse string amount correctly', () {
        expect(PaymentSessionModel.fromZohoLinkResponse({
          'data': {'amount': '1500.50'},
          'payment_link': {},
        }).amount, equals(1500.50));
      });

      test('should handle invalid string amount', () {
        expect(PaymentSessionModel.fromZohoLinkResponse({
          'data': {'amount': 'invalid'},
          'payment_link': {},
        }).amount, equals(0.0));
      });

      test('should handle null amount', () {
        expect(PaymentSessionModel.fromZohoLinkResponse({
          'data': {'amount': null},
          'payment_link': {},
        }).amount, equals(0.0));
      });

      test('should extract numeric value from formatted string', () {
        expect(PaymentSessionModel.fromZohoLinkResponse({
          'data': {'amount': '₹1,500.50'},
          'payment_link': {},
        }).amount, equals(1500.50));
      });
    });

    group('_parseCreatedTime', () {
      test('should parse int created time correctly', () {
        expect(PaymentSessionModel.fromZohoLinkResponse({
          'data': {'created_time': 1751043827},
          'payment_link': {},
        }).createdTime, equals(1751043827));
      });

      test('should parse string created time correctly', () {
        expect(PaymentSessionModel.fromZohoLinkResponse({
          'data': {'created_time': '1751043827'},
          'payment_link': {},
        }).createdTime, equals(1751043827));
      });

      test('should parse double created time correctly', () {
        expect(PaymentSessionModel.fromZohoLinkResponse({
          'data': {'created_time': 1751043827.0},
          'payment_link': {},
        }).createdTime, equals(1751043827));
      });

      test('should handle invalid string created time', () {
        expect(PaymentSessionModel.fromZohoLinkResponse({
          'data': {'created_time': 'invalid'},
          'payment_link': {},
        }).createdTime, equals(0));
      });

      test('should handle null created time', () {
        expect(PaymentSessionModel.fromZohoLinkResponse({
          'data': {'created_time': null},
          'payment_link': {},
        }).createdTime, equals(0));
      });
    });

    group('toEntity', () {
      test('should convert to PaymentSession entity correctly', () {
        // Arrange
        final model = PaymentSessionModel.fromZohoLinkResponse(testZohoLinkResponse);

        // Act
        final entity = model.toEntity();

        // Assert
        expect(entity, isA<PaymentSession>());
        expect(entity.sessionId, equals(model.sessionId));
        expect(entity.sessionUrl, equals(model.sessionUrl));
        expect(entity.paymentSessionId, equals(model.paymentSessionId));
        expect(entity.currency, equals(model.currency));
        expect(entity.amount, equals(model.amount));
        expect(entity.createdTime, equals(model.createdTime));
        expect(entity.metaData, hasLength(model.metaData.length));
      });
    });

    group('toJson', () {
      test('should convert to JSON correctly', () {
        // Arrange
        final model = PaymentSessionModel.fromZohoLinkResponse(testZohoLinkResponse);

        // Act
        final json = model.toJson();

        // Assert
        expect(json['session_id'], equals(model.sessionId));
        expect(json['session_url'], equals(model.sessionUrl));
        expect(json['payment_session_id'], equals(model.paymentSessionId));
        expect(json['currency'], equals(model.currency));
        expect(json['amount'], equals(model.amount));
        expect(json['created_time'], equals(model.createdTime));
        expect(json['meta_data'], isA<List>());
      });
    });
  });
}
