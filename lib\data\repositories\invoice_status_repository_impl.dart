import 'package:dartz/dartz.dart';
import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/invoices/invoice_status_update_request.dart';
import '../../domain/entities/invoices/invoice_status_update_response.dart';
import '../../domain/repositories/invoice_status_repository.dart';
import '../datasources/remote/invoice_status_remote_data_source.dart';
import '../models/invoices/invoice_status_update_request_model.dart';

/// Implementation of invoice status repository
class InvoiceStatusRepositoryImpl implements InvoiceStatusRepository {
  final InvoiceStatusRemoteDataSource remoteDataSource;

  InvoiceStatusRepositoryImpl({
    required this.remoteDataSource,
  });

  @override
  Future<Either<Failure, InvoiceStatusUpdateResponse>> updateInvoiceStatus(
    InvoiceStatusUpdateRequest request,
  ) async {
    try {
      logger.d('InvoiceStatusRepository: Updating status for invoice ${request.invoiceId}');
      
      // Convert domain entity to data model
      final requestModel = InvoiceStatusUpdateRequestModel.fromEntity(request);
      
      // Call remote data source
      final responseModel = await remoteDataSource.updateInvoiceStatus(requestModel);
      
      // Convert data model to domain entity
      final response = responseModel.toEntity();
      
      logger.i('InvoiceStatusRepository: Successfully updated invoice status');
      logger.d('  - Invoice ID: ${response.invoiceId}');
      logger.d('  - Previous Status: ${response.previousStatus}');
      logger.d('  - Current Status: ${response.newStatus}');
      
      return Right(response);
    } on ServerException {
      logger.e('InvoiceStatusRepository: Server exception occurred');
      return Left(ServerFailure());
    } on NetworkException {
      logger.e('InvoiceStatusRepository: Network exception occurred');
      return Left(NetworkFailure());
    } on AuthException {
      logger.e('InvoiceStatusRepository: Authentication exception occurred');
      return Left(AuthFailure());
    } catch (e) {
      logger.e('InvoiceStatusRepository: Unexpected error: ${e.toString()}');
      return Left(ServerFailure());
    }
  }
}
