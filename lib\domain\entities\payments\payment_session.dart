import 'package:equatable/equatable.dart';

/// Domain entity representing a payment session for Zoho payment integration
class PaymentSession extends Equatable {
  final String sessionId;
  final String sessionUrl;
  final String paymentSessionId;
  final String currency;
  final double amount;
  final int createdTime;
  final List<PaymentMetaData> metaData;

  const PaymentSession({
    required this.sessionId,
    required this.sessionUrl,
    required this.paymentSessionId,
    required this.currency,
    required this.amount,
    required this.createdTime,
    this.metaData = const [],
  });

  @override
  List<Object?> get props => [
        sessionId,
        sessionUrl,
        paymentSessionId,
        currency,
        amount,
        createdTime,
        metaData,
      ];
}

/// Domain entity representing metadata for payment session
class PaymentMetaData extends Equatable {
  final String key;
  final String value;

  const PaymentMetaData({
    required this.key,
    required this.value,
  });

  @override
  List<Object?> get props => [key, value];
}
