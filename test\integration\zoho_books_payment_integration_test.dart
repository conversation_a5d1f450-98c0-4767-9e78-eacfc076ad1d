import 'package:aquapartner/domain/entities/invoices/invoice.dart';
import 'package:aquapartner/domain/entities/invoices/invoice_item.dart';
import 'package:aquapartner/domain/entities/payments/payment_session.dart';
import 'package:aquapartner/domain/entities/payments/zoho_books_payment_session.dart';
import 'package:flutter_test/flutter_test.dart';

/// Integration test for Zoho Books payment flow
///
/// This test validates the end-to-end Zoho Books payment integration:
/// 1. Creating a payment link using the PaymentCubit
/// 2. Loading the payment URL in the WebView
/// 3. Ensuring backward compatibility with existing payment functionality
void main() {
  group('Zoho Books Payment Integration', () {
    test('should create Zoho Books payment link and validate data', () async {
      // Test data - create a proper Invoice with all required fields
      final testInvoice = Invoice(
        invoiceId: 'test_invoice_123',
        addressId: 'addr_123',
        ageInDays: 0,
        ageTier: 'Current',
        balance: 1500.0,
        customerId: 'customer_123',
        deliveryMode: 'Standard',
        deliveryStatus: 'Pending',
        dueDate: DateTime.now().add(Duration(days: 30)),
        invoiceDate: DateTime.now(),
        invoiceNumber: 'INV001',
        invoiceStatus: 'Pending',
        subTotal: 1350.0,
        total: 1500.0,
        items: <InvoiceItem>[],
      );

      final testZohoBooksSession = ZohoBooksPaymentSession(
        invoiceId: 'INV001',
        paymentLinkUrl: 'https://books.zoho.in/checkout/test_token_123',
        amount: 1500.0,
        currency: 'INR',
        description: 'Payment for AquaPartner Invoice INV001',
        createdAt: DateTime.now(),
      );

      // Test that the payment session contains the correct data
      expect(testZohoBooksSession.invoiceId, equals('INV001'));
      expect(testZohoBooksSession.amount, equals(1500.0));
      expect(testZohoBooksSession.currency, equals('INR'));
      expect(testZohoBooksSession.paymentLinkUrl, contains('books.zoho.in'));
      expect(testZohoBooksSession.description, contains('AquaPartner Invoice'));

      // Test that the invoice data is properly structured
      expect(testInvoice.invoiceNumber, equals('INV001'));
      expect(testInvoice.total, equals(1500.0));
      expect(testInvoice.customerId, equals('customer_123'));
    });

    test(
      'should validate WebView integration with Zoho Books payment URL',
      () async {
        final testSession = ZohoBooksPaymentSession(
          invoiceId: 'INV001',
          paymentLinkUrl: 'https://books.zoho.in/checkout/test_token_123',
          amount: 1500.0,
          currency: 'INR',
          description: 'Payment for AquaPartner Invoice INV001',
          createdAt: DateTime.now(),
        );

        // Verify that the WebView can handle Zoho Books URLs
        expect(testSession.paymentLinkUrl, contains('books.zoho.in'));
        expect(testSession.paymentLinkUrl, contains('checkout'));
        expect(testSession.paymentLinkUrl, startsWith('https://'));
      },
    );

    test(
      'should maintain backward compatibility with existing payment flow',
      () async {
        // Test that both regular Zoho payments and Zoho Books payments can coexist
        final regularPaymentUrl =
            'https://payments.zoho.in/paymentlinks/test_token';
        final zohoBooksPaymentUrl = 'https://books.zoho.in/checkout/test_token';

        // Both URLs should be valid for the WebView
        expect(regularPaymentUrl, contains('zoho.in'));
        expect(zohoBooksPaymentUrl, contains('zoho.in'));

        // Both should be distinguishable
        expect(regularPaymentUrl, contains('payments.zoho.in'));
        expect(zohoBooksPaymentUrl, contains('books.zoho.in'));

        // Both should be secure HTTPS URLs
        expect(regularPaymentUrl, startsWith('https://'));
        expect(zohoBooksPaymentUrl, startsWith('https://'));
      },
    );

    test('should validate API endpoint configuration', () {
      // Test that the Zoho Books API endpoint is correctly configured
      const expectedEndpoint = '/api/zoho-books/payments/create-link';
      const baseUrl =
          'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api';

      final fullUrl = '$baseUrl/zoho-books/payments/create-link';

      expect(
        fullUrl,
        contains(
          'aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net',
        ),
      );
      expect(fullUrl, contains('zoho-books/payments/create-link'));
      expect(
        fullUrl,
        isNot(contains('zoho/payments/create-link')),
      ); // Different from regular Zoho endpoint
    });

    test('should validate payment request payload structure', () {
      // Test the Zoho Books payment request structure
      final payloadMap = {
        'invoice_id': 'INV001',
        'amount': 1500.0,
        'currency': 'INR',
        'description': 'Payment for AquaPartner Invoice INV001',
      };

      // Validate required fields
      expect(payloadMap['invoice_id'], isNotNull);
      expect(payloadMap['amount'], isA<double>());
      expect(payloadMap['currency'], equals('INR'));
      expect(payloadMap['description'], isA<String>());

      // Validate field types
      expect(payloadMap['invoice_id'], isA<String>());
      expect(payloadMap['amount'], greaterThan(0));
      expect(payloadMap['currency'], hasLength(3));
      expect(payloadMap['description'], isNotEmpty);
    });

    test('should validate payment response structure', () {
      // Test the expected Zoho Books payment response structure
      final responseMap = {
        'payment_link_url': 'https://books.zoho.in/checkout/test_token_123',
        'status': 'success',
      };

      // Validate response fields
      expect(responseMap['payment_link_url'], isNotNull);
      expect(responseMap['payment_link_url'], contains('books.zoho.in'));
      expect(responseMap['payment_link_url'], contains('checkout'));
      expect(responseMap['status'], equals('success'));
    });

    test('should validate error handling scenarios', () {
      // Test various error scenarios that should be handled
      final errorScenarios = [
        'Network connection failed',
        'Invalid invoice ID',
        'Amount must be greater than zero',
        'Currency is required',
        'Description is required',
        'Server error occurred',
        'Authentication failed',
      ];

      for (final errorMessage in errorScenarios) {
        expect(errorMessage, isA<String>());
        expect(errorMessage, isNotEmpty);
      }
    });

    test('should validate WebView URL monitoring patterns', () {
      // Test URL patterns that the WebView should monitor for completion
      final completionUrls = [
        'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/payment-success',
        'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/payment-failure',
        'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/payment-cancelled',
      ];

      for (final url in completionUrls) {
        expect(
          url,
          contains(
            'aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net',
          ),
        );
        expect(url, contains('payment-'));
      }
    });
  });
}
