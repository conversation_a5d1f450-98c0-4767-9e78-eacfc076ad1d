import 'package:aquapartner/domain/entities/invoices/invoice.dart';
import 'package:aquapartner/domain/entities/invoices/invoice_item.dart';
import 'package:aquapartner/domain/entities/payments/zoho_books_payment_request.dart';
import 'package:flutter_test/flutter_test.dart';

/// Debug test to verify Zoho Books payment request creation
/// This helps validate the invoice ID mapping fix
void main() {
  group('Zoho Books Payment Debug Tests', () {
    test('should create correct payment request with proper invoice ID', () {
      // Create a test invoice with the same structure as real data
      final testInvoice = Invoice(
        invoiceId: '401088000082205103', // This should be the Zoho Books invoice ID
        addressId: 'addr_123',
        ageInDays: 5,
        ageTier: 'Current',
        balance: 3.00,
        customerId: 'customer_123',
        deliveryMode: 'Standard',
        deliveryStatus: 'Pending',
        dueDate: DateTime.now().add(Duration(days: 30)),
        invoiceDate: DateTime.now(),
        invoiceNumber: 'INV-2024-001', // This is the human-readable number
        invoiceStatus: 'Overdue',
        subTotal: 2.70,
        total: 3.00,
        items: <InvoiceItem>[],
      );

      // Create Zoho Books payment request (same logic as in PaymentCubit)
      final zohoBooksRequest = ZohoBooksPaymentRequest(
        invoiceId: testInvoice.invoiceId, // Should use invoiceId, not invoiceNumber
        amount: testInvoice.total,
        currency: 'INR',
        description: 'Payment for AquaPartner Invoice ${testInvoice.invoiceNumber}',
      );

      // Verify the request has correct values
      expect(zohoBooksRequest.invoiceId, equals('401088000082205103'));
      expect(zohoBooksRequest.amount, equals(3.00));
      expect(zohoBooksRequest.currency, equals('INR'));
      expect(zohoBooksRequest.description, contains('INV-2024-001'));

      // Print debug information
      print('=== Zoho Books Payment Request Debug ===');
      print('Invoice ID (Zoho Books): ${zohoBooksRequest.invoiceId}');
      print('Invoice Number (Display): ${testInvoice.invoiceNumber}');
      print('Amount: ${zohoBooksRequest.amount}');
      print('Currency: ${zohoBooksRequest.currency}');
      print('Description: ${zohoBooksRequest.description}');
      print('========================================');
    });

    test('should validate invoice ID format', () {
      // Test with different invoice ID formats to understand the pattern
      final testCases = [
        '401088000082205103', // Working Postman example
        'INV-2024-001',       // Typical invoice number format
        '',                   // Empty case
        '123456789',          // Simple numeric
      ];

      for (final invoiceId in testCases) {
        print('Testing invoice ID: "$invoiceId"');
        print('  - Length: ${invoiceId.length}');
        print('  - Is numeric: ${RegExp(r'^\d+$').hasMatch(invoiceId)}');
        print('  - Is empty: ${invoiceId.isEmpty}');
        print('  - Starts with 4010: ${invoiceId.startsWith('4010')}');
      }
    });

    test('should create JSON payload matching Postman request', () {
      // Create the exact same request as the working Postman example
      final zohoBooksRequest = ZohoBooksPaymentRequest(
        invoiceId: '401088000082205103',
        amount: 3.00,
        currency: 'INR',
        description: 'Test payment from Postman',
      );

      // Convert to JSON (same as ZohoBooksPaymentRequestModel.toJson())
      final jsonPayload = {
        'invoice_id': zohoBooksRequest.invoiceId,
        'amount': zohoBooksRequest.amount,
        'currency': zohoBooksRequest.currency,
        'description': zohoBooksRequest.description,
      };

      // Verify JSON structure matches Postman
      expect(jsonPayload['invoice_id'], equals('401088000082205103'));
      expect(jsonPayload['amount'], equals(3.00));
      expect(jsonPayload['currency'], equals('INR'));
      expect(jsonPayload['description'], isA<String>());

      print('=== JSON Payload Comparison ===');
      print('Generated JSON: $jsonPayload');
      print('Expected (Postman): {');
      print('  "invoice_id": "401088000082205103",');
      print('  "amount": 3.00,');
      print('  "currency": "INR",');
      print('  "description": "Test payment from Postman"');
      print('}');
      print('===============================');
    });

    test('should identify potential invoice ID issues', () {
      // Test scenarios that might cause API failures
      final problematicCases = [
        {
          'invoiceId': 'INV-2024-001',
          'issue': 'Using invoice number instead of Zoho Books ID'
        },
        {
          'invoiceId': '',
          'issue': 'Empty invoice ID'
        },
        {
          'invoiceId': null,
          'issue': 'Null invoice ID'
        },
        {
          'invoiceId': '401088000082205103',
          'issue': 'Correct format (should work)'
        },
      ];

      for (final testCase in problematicCases) {
        final invoiceId = testCase['invoiceId'];
        final issue = testCase['issue'];
        
        print('Test Case: $issue');
        print('  Invoice ID: "$invoiceId"');
        
        if (invoiceId == null || invoiceId.toString().isEmpty) {
          print('  Result: ❌ WILL FAIL - Empty/null invoice ID');
        } else if (!RegExp(r'^\d+$').hasMatch(invoiceId.toString())) {
          print('  Result: ⚠️  MIGHT FAIL - Non-numeric invoice ID');
        } else if (invoiceId.toString().length < 10) {
          print('  Result: ⚠️  MIGHT FAIL - Invoice ID too short');
        } else {
          print('  Result: ✅ SHOULD WORK - Proper format');
        }
        print('');
      }
    });
  });
}
