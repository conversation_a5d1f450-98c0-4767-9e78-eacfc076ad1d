import 'package:equatable/equatable.dart';

/// Domain entity representing a payment request for creating a payment session
class PaymentRequest extends Equatable {
  final double amount;
  final String currencyCode;
  final String customerId;
  final String invoiceNumber;
  final String description;
  final String? customerName;
  final String? customerEmail;
  final String? customerPhone;
  final String? redirectUrl;
  final String? referenceId;
  final List<PaymentRequestMetaData> metaData;

  const PaymentRequest({
    required this.amount,
    required this.currencyCode,
    required this.customerId,
    required this.invoiceNumber,
    required this.description,
    this.customerName,
    this.customerEmail,
    this.customerPhone,
    this.redirectUrl,
    this.referenceId,
    this.metaData = const [],
  });

  @override
  List<Object?> get props => [
        amount,
        currencyCode,
        customerId,
        invoiceNumber,
        description,
        customerName,
        customerEmail,
        customerPhone,
        redirectUrl,
        referenceId,
        metaData,
      ];
}

/// Domain entity representing metadata for payment request
class PaymentRequestMetaData extends Equatable {
  final String key;
  final String value;

  const PaymentRequestMetaData({
    required this.key,
    required this.value,
  });

  @override
  List<Object?> get props => [key, value];
}
