import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/error/failures.dart';
import '../../../core/services/analytics_service.dart';
import '../../../core/utils/logger.dart';
import '../../../domain/entities/customer.dart';
import '../../../domain/entities/invoices/invoice.dart';
import '../../../domain/entities/payments/payment_request.dart';
import '../../../domain/entities/payments/payment_transaction.dart';
import '../../../domain/entities/payments/zoho_books_payment_request.dart';
import '../../../domain/services/auth_service.dart';
import '../../../domain/usecases/payments/create_payment_session_usecase.dart';
import '../../../domain/usecases/payments/create_zoho_books_payment_session_usecase.dart';
import '../../../domain/usecases/payments/get_payment_status_usecase.dart';
import '../../../domain/usecases/payments/process_payment_usecase.dart';
import 'payment_state.dart';

/// Cubit for managing payment operations
class PaymentCubit extends Cubit<PaymentState> {
  final CreatePaymentSessionUseCase createPaymentSessionUseCase;
  final ProcessPaymentUseCase processPaymentUseCase;
  final GetPaymentStatusUseCase getPaymentStatusUseCase;
  final CreateZohoBooksPaymentSessionUseCase
  createZohoBooksPaymentSessionUseCase;
  final AuthService authService;
  final AppLogger logger;
  final AnalyticsService _analyticsService;

  PaymentCubit({
    required this.createPaymentSessionUseCase,
    required this.processPaymentUseCase,
    required this.getPaymentStatusUseCase,
    required this.createZohoBooksPaymentSessionUseCase,
    required this.authService,
    required this.logger,
    required AnalyticsService analyticsService,
  }) : _analyticsService = analyticsService,
       super(PaymentInitial());

  /// The name of the screen for analytics tracking
  String get screenName => 'payment_cubit';

  /// Tracks an analytics event with standardized parameters
  void _trackEvent(String eventName, {Map<String, Object> params = const {}}) {
    final eventParams = {
      'screen_name': screenName,
      'timestamp': DateTime.now().toIso8601String(),
      ...params,
    };

    _analyticsService.logEvent(name: eventName, parameters: eventParams);
  }

  /// Creates a payment session for the given invoice
  Future<void> createPaymentSession(Invoice invoice) async {
    try {
      emit(PaymentSessionLoading());

      // Track payment initiation
      _trackEvent(
        'payment_session_creation_started',
        params: {
          'invoice_number': invoice.invoiceNumber,
          'invoice_amount': invoice.total.toString(),
          'invoice_status': invoice.invoiceStatus,
        },
      );

      // Get current customer
      final customerResult = await authService.getCurrentCustomer();

      Customer? customer;
      customerResult.fold(
        (failure) {
          logger.e('Failed to get customer: ${failure.toString()}');
          emit(
            PaymentFailure(
              message: 'Unable to retrieve customer information',
              invoiceNumber: invoice.invoiceNumber,
            ),
          );
          return;
        },
        (customerData) {
          customer = customerData;
        },
      );

      if (customer == null) {
        emit(
          PaymentFailure(
            message: 'Customer information not found',
            invoiceNumber: invoice.invoiceNumber,
          ),
        );
        return;
      }

      // Create payment request
      final paymentRequest = PaymentRequest(
        amount: invoice.total,
        currencyCode: 'INR',
        customerId: customer!.customerId,
        invoiceNumber: invoice.invoiceNumber,
        description: 'Payment for AquaPartner Invoice ${invoice.invoiceNumber}',
        customerName: customer!.customerName,
        customerEmail: customer!.email,
        customerPhone: customer!.mobileNumber,
      );

      // Create payment session
      final result = await createPaymentSessionUseCase(paymentRequest);

      result.fold(
        (failure) {
          final errorMessage = _mapFailureToMessage(failure);
          logger.e('Payment session creation failed: $errorMessage');

          // Track payment session creation failure
          _trackEvent(
            'payment_session_creation_failed',
            params: {
              'invoice_number': invoice.invoiceNumber,
              'error_message': errorMessage,
              'failure_type': failure.runtimeType.toString(),
            },
          );

          emit(
            PaymentFailure(
              message: errorMessage,
              invoiceNumber: invoice.invoiceNumber,
            ),
          );
        },
        (session) {
          logger.i(
            'Payment session created successfully: ${session.sessionId}',
          );

          // Track payment session creation success
          _trackEvent(
            'payment_session_creation_success',
            params: {
              'invoice_number': invoice.invoiceNumber,
              'session_id': session.sessionId,
              'amount': session.amount.toString(),
            },
          );

          emit(PaymentSessionCreated(session));
        },
      );
    } catch (e) {
      logger.e('Unexpected error in createPaymentSession: ${e.toString()}');

      // Track unexpected error
      _trackEvent(
        'payment_session_creation_error',
        params: {
          'invoice_number': invoice.invoiceNumber,
          'error': e.toString(),
        },
      );

      emit(
        PaymentFailure(
          message: 'An unexpected error occurred. Please try again.',
          invoiceNumber: invoice.invoiceNumber,
        ),
      );
    }
  }

  /// Processes payment for the given session
  Future<void> processPayment({
    required String sessionId,
    required String invoiceNumber,
    required double amount,
    required String currency,
  }) async {
    try {
      emit(
        PaymentProcessing(sessionId: sessionId, invoiceNumber: invoiceNumber),
      );

      // Track payment processing start
      _trackEvent(
        'payment_processing_started',
        params: {
          'session_id': sessionId,
          'invoice_number': invoiceNumber,
          'amount': amount.toString(),
        },
      );

      final result = await processPaymentUseCase(
        sessionId: sessionId,
        invoiceNumber: invoiceNumber,
        amount: amount,
        currency: currency,
      );

      result.fold(
        (failure) {
          final errorMessage = _mapFailureToMessage(failure);
          logger.e('Payment processing failed: $errorMessage');

          // Track payment processing failure
          _trackEvent(
            'payment_processing_failed',
            params: {
              'session_id': sessionId,
              'invoice_number': invoiceNumber,
              'error_message': errorMessage,
            },
          );

          emit(
            PaymentFailure(message: errorMessage, invoiceNumber: invoiceNumber),
          );
        },
        (transaction) {
          logger.i(
            'Payment processed successfully: ${transaction.transactionId}',
          );

          // Track payment processing success
          _trackEvent(
            'payment_processing_success',
            params: {
              'transaction_id': transaction.transactionId,
              'session_id': sessionId,
              'invoice_number': invoiceNumber,
              'amount': amount.toString(),
            },
          );

          emit(PaymentSuccess(transaction));
        },
      );
    } catch (e) {
      logger.e('Unexpected error in processPayment: ${e.toString()}');

      // Track unexpected error
      _trackEvent(
        'payment_processing_error',
        params: {
          'session_id': sessionId,
          'invoice_number': invoiceNumber,
          'error': e.toString(),
        },
      );

      emit(
        PaymentFailure(
          message: 'An unexpected error occurred during payment processing.',
          invoiceNumber: invoiceNumber,
        ),
      );
    }
  }

  /// Handles payment cancellation
  void cancelPayment({
    required String sessionId,
    required String invoiceNumber,
  }) {
    logger.i('Payment cancelled for session: $sessionId');

    // Track payment cancellation
    _trackEvent(
      'payment_cancelled',
      params: {'session_id': sessionId, 'invoice_number': invoiceNumber},
    );

    emit(PaymentCancelled(sessionId: sessionId, invoiceNumber: invoiceNumber));
  }

  /// Gets payment status for a transaction
  Future<void> getPaymentStatus(String transactionId) async {
    try {
      emit(PaymentStatusLoading(transactionId));

      final result = await getPaymentStatusUseCase(transactionId);

      result.fold(
        (failure) {
          final errorMessage = _mapFailureToMessage(failure);
          logger.e('Failed to get payment status: $errorMessage');
          emit(PaymentFailure(message: errorMessage));
        },
        (transaction) {
          logger.i('Payment status retrieved: ${transaction.status}');
          emit(PaymentStatusLoaded(transaction));
        },
      );
    } catch (e) {
      logger.e('Unexpected error in getPaymentStatus: ${e.toString()}');
      emit(PaymentFailure(message: 'Failed to retrieve payment status.'));
    }
  }

  /// Creates a Zoho Books payment link for the given invoice
  Future<void> createZohoBooksPaymentLink(Invoice invoice) async {
    try {
      emit(ZohoBooksPaymentLinkLoading());

      _trackEvent(
        'zoho_books_payment_link_creation_started',
        params: {
          'invoice_number': invoice.invoiceNumber,
          'amount': invoice.total,
        },
      );

      logger.i(
        'Creating Zoho Books payment link for invoice: ${invoice.invoiceNumber}',
      );

      // Validate invoice data before making API call
      if (invoice.invoiceId.isEmpty) {
        logger.e('Invoice ID is empty - cannot create Zoho Books payment link');
        emit(
          ZohoBooksPaymentFailure(
            message: 'Invalid invoice data: Invoice ID is missing',
            invoiceId: invoice.invoiceNumber,
          ),
        );
        return;
      }

      if (invoice.total <= 0) {
        logger.e('Invoice amount is invalid: ${invoice.total}');
        emit(
          ZohoBooksPaymentFailure(
            message: 'Invalid invoice amount: ${invoice.total}',
            invoiceId: invoice.invoiceNumber,
          ),
        );
        return;
      }

      // Create Zoho Books payment request
      final zohoBooksRequest = ZohoBooksPaymentRequest(
        invoiceId:
            invoice
                .invoiceId, // Using invoice ID (Zoho Books ID) instead of invoice number
        amount: invoice.total,
        currency: 'INR',
        description: 'Payment for AquaPartner Invoice ${invoice.invoiceNumber}',
      );

      // Log the request details for debugging
      logger.d('Zoho Books payment request details:');
      logger.d('  - Invoice ID (Zoho Books): ${invoice.invoiceId}');
      logger.d('  - Invoice Number (Display): ${invoice.invoiceNumber}');
      logger.d('  - Amount: ${invoice.total}');
      logger.d('  - Currency: INR');
      logger.d('  - Description: ${zohoBooksRequest.description}');

      // Create payment link
      final result = await createZohoBooksPaymentSessionUseCase(
        zohoBooksRequest,
      );

      result.fold(
        (failure) {
          final errorMessage = _mapFailureToMessage(failure);
          logger.e('Failed to create Zoho Books payment link: $errorMessage');
          logger.e('Failure details:');
          logger.e('  - Type: ${failure.runtimeType}');
          logger.e('  - Invoice ID: ${invoice.invoiceId}');
          logger.e('  - Invoice Number: ${invoice.invoiceNumber}');
          logger.e('  - Amount: ${invoice.total}');

          // Add specific logging for ServerFailure to help identify response parsing issues
          if (failure is ServerFailure) {
            logger.e(
              'ServerFailure detected - likely API response parsing issue',
            );
            logger.e(
              'This suggests the API call succeeded but response parsing failed',
            );
            logger.e('Check the API response structure in the logs above');
          }

          _trackEvent(
            'zoho_books_payment_link_creation_failed',
            params: {
              'invoice_number': invoice.invoiceNumber,
              'invoice_id': invoice.invoiceId,
              'error_message': errorMessage,
              'failure_type': failure.runtimeType.toString(),
              'amount': invoice.total.toString(),
            },
          );

          emit(
            ZohoBooksPaymentFailure(
              message: errorMessage,
              invoiceId: invoice.invoiceNumber,
            ),
          );
        },
        (session) {
          logger.i(
            'Zoho Books payment link created successfully: ${session.paymentLinkUrl}',
          );

          _trackEvent(
            'zoho_books_payment_link_creation_success',
            params: {
              'invoice_number': invoice.invoiceNumber,
              'payment_link_url': session.paymentLinkUrl,
              'amount': session.amount,
            },
          );

          emit(ZohoBooksPaymentLinkCreated(session));
        },
      );
    } catch (e) {
      logger.e(
        'Unexpected error creating Zoho Books payment link: ${e.toString()}',
      );

      _trackEvent(
        'zoho_books_payment_link_creation_error',
        params: {
          'invoice_number': invoice.invoiceNumber,
          'error': e.toString(),
        },
      );

      emit(
        ZohoBooksPaymentFailure(
          message: 'An unexpected error occurred. Please try again.',
          invoiceId: invoice.invoiceNumber,
        ),
      );
    }
  }

  /// Resets payment state to initial
  void resetPaymentState() {
    emit(PaymentInitial());
  }

  /// Maps failure to user-friendly message
  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case NetworkFailure:
        return 'No internet connection. Please check your network and try again.';
      case ServerFailure:
        return 'Server error occurred. Please try again later.';
      case AuthFailure:
        return 'Authentication failed. Please login again.';
      case ValidationFailure:
        return (failure as ValidationFailure).message;
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }
}
