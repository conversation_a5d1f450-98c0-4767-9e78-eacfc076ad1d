import 'dart:convert';
import '../../../core/constants/app_constants.dart';
import '../../../core/error/exceptions.dart';
import '../../../core/network/api_client.dart';
import '../../../core/utils/logger.dart';
import '../../models/payments/zoho_books_payment_request_model.dart';
import '../../models/payments/zoho_books_payment_session_model.dart';

/// Abstract interface for Zoho Books payment remote data source
abstract class ZohoBooksPaymentRemoteDataSource {
  /// Creates a payment link with Zoho Books payment gateway
  Future<ZohoBooksPaymentSessionModel> createPaymentLink(ZohoBooksPaymentRequestModel request);
}

/// Implementation of Zoho Books payment remote data source
class ZohoBooksPaymentRemoteDataSourceImpl implements ZohoBooksPaymentRemoteDataSource {
  final ApiClient apiClient;
  final AppLogger logger;

  ZohoBooksPaymentRemoteDataSourceImpl({
    required this.apiClient,
    required this.logger,
  });

  @override
  Future<ZohoBooksPaymentSessionModel> createPaymentLink(
    ZohoBooksPaymentRequestModel request,
  ) async {
    try {
      logger.i('Creating Zoho Books payment link for invoice: ${request.invoiceId}');

      // Prepare the request payload for Zoho Books API
      final requestPayload = request.toJson();

      logger.d('Zoho Books payment request payload: $requestPayload');

      // Call the Zoho Books payment link creation endpoint
      final response = await apiClient.post(
        '${AppConstants.baseUrl}/zoho-books/payments/create-link',
        data: requestPayload,
      );

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.data) as Map<String, dynamic>;
        logger.i('Zoho Books payment link created successfully');
        logger.d('Response data: $jsonData');

        // Create session model with the response data
        final sessionData = {
          'invoice_id': request.invoiceId,
          'payment_link_url': jsonData['payment_link_url'],
          'amount': request.amount,
          'currency': request.currency,
          'description': request.description,
          'created_at': DateTime.now().toIso8601String(),
        };

        return ZohoBooksPaymentSessionModel.fromJson(sessionData);
      } else {
        logger.e('Failed to create Zoho Books payment link: ${response.statusCode}');
        throw ServerException();
      }
    } catch (e) {
      logger.e('Error creating Zoho Books payment link: ${e.toString()}');
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException();
    }
  }
}
