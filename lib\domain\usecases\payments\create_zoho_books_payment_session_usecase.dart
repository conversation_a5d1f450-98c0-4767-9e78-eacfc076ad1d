import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../entities/payments/zoho_books_payment_request.dart';
import '../../entities/payments/zoho_books_payment_session.dart';
import '../../repositories/zoho_books_payment_repository.dart';

/// Use case for creating a payment link with Zoho Books payment gateway
class CreateZohoBooksPaymentSessionUseCase {
  final ZohoBooksPaymentRepository repository;

  CreateZohoBooksPaymentSessionUseCase(this.repository);

  /// Creates a payment link for the given Zoho Books payment request
  /// 
  /// Returns [ZohoBooksPaymentSession] on success or [Failure] on error
  Future<Either<Failure, ZohoBooksPaymentSession>> call(ZohoBooksPaymentRequest request) async {
    // Validate payment request
    if (request.amount <= 0) {
      return Left(ValidationFailure('Amount must be greater than zero'));
    }

    if (request.invoiceId.isEmpty) {
      return Left(ValidationFailure('Invoice ID is required'));
    }

    if (request.description.isEmpty) {
      return Left(ValidationFailure('Description is required'));
    }

    if (request.currency.isEmpty) {
      return Left(ValidationFailure('Currency is required'));
    }

    // Call repository to create payment link
    return await repository.createPaymentLink(request);
  }
}
