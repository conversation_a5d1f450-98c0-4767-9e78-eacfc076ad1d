import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../entities/payments/payment_transaction.dart';
import '../../repositories/payment_repository.dart';

/// Use case for processing payment transactions
class ProcessPaymentUseCase {
  final PaymentRepository repository;

  ProcessPaymentUseCase(this.repository);

  /// Processes a payment transaction for the given session ID
  /// 
  /// Returns [PaymentTransaction] on success or [Failure] on error
  Future<Either<Failure, PaymentTransaction>> call({
    required String sessionId,
    required String invoiceNumber,
    required double amount,
    required String currency,
  }) async {
    // Validate input parameters
    if (sessionId.isEmpty) {
      return Left(ValidationFailure('Session ID is required'));
    }

    if (invoiceNumber.isEmpty) {
      return Left(ValidationFailure('Invoice number is required'));
    }

    if (amount <= 0) {
      return Left(ValidationFailure('Amount must be greater than zero'));
    }

    if (currency.isEmpty) {
      return Left(ValidationFailure('Currency is required'));
    }

    // Call repository to process payment
    return await repository.processPayment(
      sessionId: sessionId,
      invoiceNumber: invoiceNumber,
      amount: amount,
      currency: currency,
    );
  }
}
