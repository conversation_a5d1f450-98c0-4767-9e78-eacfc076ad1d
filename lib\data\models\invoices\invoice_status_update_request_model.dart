import '../../../domain/entities/invoices/invoice_status_update_request.dart';

/// Data model for payment details in invoice status update request
class PaymentDetailsModel {
  final String paymentMethod;
  final double paymentAmount;
  final String transactionId;
  final String paymentDate;

  PaymentDetailsModel({
    required this.paymentMethod,
    required this.paymentAmount,
    required this.transactionId,
    required this.paymentDate,
  });

  /// Creates PaymentDetailsModel from domain entity
  factory PaymentDetailsModel.fromEntity(PaymentDetails entity) {
    return PaymentDetailsModel(
      paymentMethod: entity.paymentMethod,
      paymentAmount: entity.paymentAmount,
      transactionId: entity.transactionId,
      paymentDate: entity.paymentDate.toIso8601String(),
    );
  }

  /// Converts to JSON for API request
  Map<String, dynamic> toJson() {
    return {
      'payment_method': paymentMethod,
      'payment_amount': paymentAmount,
      'transaction_id': transactionId,
      'payment_date': paymentDate,
    };
  }

  /// Converts to domain entity
  PaymentDetails toEntity() {
    return PaymentDetails(
      paymentMethod: paymentMethod,
      paymentAmount: paymentAmount,
      transactionId: transactionId,
      paymentDate: DateTime.parse(paymentDate),
    );
  }
}

/// Data model for invoice status update request
class InvoiceStatusUpdateRequestModel {
  final String invoiceId;
  final PaymentDetailsModel paymentDetails;

  InvoiceStatusUpdateRequestModel({
    required this.invoiceId,
    required this.paymentDetails,
  });

  /// Creates InvoiceStatusUpdateRequestModel from domain entity
  factory InvoiceStatusUpdateRequestModel.fromEntity(InvoiceStatusUpdateRequest entity) {
    return InvoiceStatusUpdateRequestModel(
      invoiceId: entity.invoiceId,
      paymentDetails: PaymentDetailsModel.fromEntity(entity.paymentDetails),
    );
  }

  /// Converts to JSON for API request
  Map<String, dynamic> toJson() {
    return {
      'invoiceId': invoiceId,
      'paymentDetails': paymentDetails.toJson(),
    };
  }

  /// Converts to domain entity
  InvoiceStatusUpdateRequest toEntity() {
    return InvoiceStatusUpdateRequest(
      invoiceId: invoiceId,
      paymentDetails: paymentDetails.toEntity(),
    );
  }
}
