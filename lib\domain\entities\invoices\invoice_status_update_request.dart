import 'package:equatable/equatable.dart';

/// Domain entity representing payment details for invoice status update
class PaymentDetails extends Equatable {
  final String paymentMethod;
  final double paymentAmount;
  final String transactionId;
  final DateTime paymentDate;

  const PaymentDetails({
    required this.paymentMethod,
    required this.paymentAmount,
    required this.transactionId,
    required this.paymentDate,
  });

  @override
  List<Object?> get props => [
        paymentMethod,
        paymentAmount,
        transactionId,
        paymentDate,
      ];
}

/// Domain entity representing an invoice status update request
class InvoiceStatusUpdateRequest extends Equatable {
  final String invoiceId;
  final PaymentDetails paymentDetails;

  const InvoiceStatusUpdateRequest({
    required this.invoiceId,
    required this.paymentDetails,
  });

  @override
  List<Object?> get props => [
        invoiceId,
        paymentDetails,
      ];

  /// Factory method to create request from invoice and payment result
  factory InvoiceStatusUpdateRequest.fromPaymentSuccess({
    required String invoiceId,
    required double paymentAmount,
    required String? transactionId,
    DateTime? paymentDate,
  }) {
    return InvoiceStatusUpdateRequest(
      invoiceId: invoiceId,
      paymentDetails: PaymentDetails(
        paymentMethod: 'zoho_books_payment_link',
        paymentAmount: paymentAmount,
        transactionId: transactionId ?? 'TXN-FLUTTER-${DateTime.now().millisecondsSinceEpoch}',
        paymentDate: paymentDate ?? DateTime.now(),
      ),
    );
  }
}
