import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../entities/payments/payment_transaction.dart';
import '../../repositories/payment_repository.dart';

/// Use case for getting payment transaction status
class GetPaymentStatusUseCase {
  final PaymentRepository repository;

  GetPaymentStatusUseCase(this.repository);

  /// Gets the payment status for the given transaction ID
  /// 
  /// Returns [PaymentTransaction] on success or [Failure] on error
  Future<Either<Failure, PaymentTransaction>> call(String transactionId) async {
    // Validate transaction ID
    if (transactionId.isEmpty) {
      return Left(ValidationFailure('Transaction ID is required'));
    }

    // Call repository to get payment status
    return await repository.getPaymentStatus(transactionId);
  }
}
