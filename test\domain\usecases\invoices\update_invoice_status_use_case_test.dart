import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/domain/entities/invoices/invoice_status_update_request.dart';
import 'package:aquapartner/domain/entities/invoices/invoice_status_update_response.dart';
import 'package:aquapartner/domain/repositories/invoice_status_repository.dart';
import 'package:aquapartner/domain/usecases/invoices/update_invoice_status_use_case.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'update_invoice_status_use_case_test.mocks.dart';

@GenerateMocks([InvoiceStatusRepository])
void main() {
  late UpdateInvoiceStatusUseCase useCase;
  late MockInvoiceStatusRepository mockRepository;

  setUp(() {
    mockRepository = MockInvoiceStatusRepository();
    useCase = UpdateInvoiceStatusUseCase(mockRepository);
  });

  group('UpdateInvoiceStatusUseCase', () {
    const tInvoiceId = '401088000082205103';
    const tPaymentAmount = 3.00;
    const tTransactionId = 'TXN-FLUTTER-123';
    final tPaymentDate = DateTime.parse('2025-07-10T10:30:00.000Z');

    final tRequest = InvoiceStatusUpdateRequest.fromPaymentSuccess(
      invoiceId: tInvoiceId,
      paymentAmount: tPaymentAmount,
      transactionId: tTransactionId,
      paymentDate: tPaymentDate,
    );

    final tResponseData = InvoiceStatusUpdateData(
      invoiceId: tInvoiceId,
      previousStatus: 'Overdue',
      currentStatus: 'Paid',
      paymentCompletedAt: tPaymentDate,
      updatedAt: DateTime.now(),
    );

    final tResponse = InvoiceStatusUpdateResponse(
      success: true,
      message: 'Invoice status updated successfully',
      data: tResponseData,
    );

    test('should update invoice status successfully', () async {
      // arrange
      when(
        mockRepository.updateInvoiceStatus(any),
      ).thenAnswer((_) async => Right(tResponse));

      // act
      final result = await useCase(tRequest);

      // assert
      expect(result, Right(tResponse));
      verify(mockRepository.updateInvoiceStatus(tRequest));
      verifyNoMoreInteractions(mockRepository);
    });

    test('should return failure when repository fails', () async {
      // arrange
      when(
        mockRepository.updateInvoiceStatus(any),
      ).thenAnswer((_) async => Left(ServerFailure()));

      // act
      final result = await useCase(tRequest);

      // assert
      expect(result, Left(ServerFailure()));
      verify(mockRepository.updateInvoiceStatus(tRequest));
      verifyNoMoreInteractions(mockRepository);
    });

    test(
      'should create request from payment success data using convenience method',
      () async {
        // arrange
        when(
          mockRepository.updateInvoiceStatus(any),
        ).thenAnswer((_) async => Right(tResponse));

        // act
        final result = await useCase.updateFromPaymentSuccess(
          invoiceId: tInvoiceId,
          paymentAmount: tPaymentAmount,
          transactionId: tTransactionId,
          paymentDate: tPaymentDate,
        );

        // assert
        expect(result, Right(tResponse));
        verify(mockRepository.updateInvoiceStatus(any));
        verifyNoMoreInteractions(mockRepository);
      },
    );

    test('should handle network failure', () async {
      // arrange
      when(
        mockRepository.updateInvoiceStatus(any),
      ).thenAnswer((_) async => Left(NetworkFailure()));

      // act
      final result = await useCase(tRequest);

      // assert
      expect(result, Left(NetworkFailure()));
      verify(mockRepository.updateInvoiceStatus(tRequest));
      verifyNoMoreInteractions(mockRepository);
    });

    test('should handle authentication failure', () async {
      // arrange
      when(
        mockRepository.updateInvoiceStatus(any),
      ).thenAnswer((_) async => Left(AuthFailure()));

      // act
      final result = await useCase(tRequest);

      // assert
      expect(result, Left(AuthFailure()));
      verify(mockRepository.updateInvoiceStatus(tRequest));
      verifyNoMoreInteractions(mockRepository);
    });

    test(
      'should create request with default transaction ID when not provided',
      () async {
        // arrange
        when(
          mockRepository.updateInvoiceStatus(any),
        ).thenAnswer((_) async => Right(tResponse));

        // act
        final result = await useCase.updateFromPaymentSuccess(
          invoiceId: tInvoiceId,
          paymentAmount: tPaymentAmount,
          transactionId: null, // No transaction ID provided
        );

        // assert
        expect(result, Right(tResponse));

        // Verify that a request was made with a generated transaction ID
        final captured =
            verify(mockRepository.updateInvoiceStatus(captureAny)).captured;
        final capturedRequest = captured.first as InvoiceStatusUpdateRequest;
        expect(capturedRequest.invoiceId, equals(tInvoiceId));
        expect(
          capturedRequest.paymentDetails.paymentAmount,
          equals(tPaymentAmount),
        );
        expect(
          capturedRequest.paymentDetails.transactionId,
          startsWith('TXN-FLUTTER-'),
        );
        expect(
          capturedRequest.paymentDetails.paymentMethod,
          equals('zoho_books_payment_link'),
        );
      },
    );

    test(
      'should create request with current timestamp when payment date not provided',
      () async {
        // arrange
        when(
          mockRepository.updateInvoiceStatus(any),
        ).thenAnswer((_) async => Right(tResponse));

        final beforeCall = DateTime.now();

        // act
        final result = await useCase.updateFromPaymentSuccess(
          invoiceId: tInvoiceId,
          paymentAmount: tPaymentAmount,
          transactionId: tTransactionId,
          // paymentDate not provided
        );

        // assert
        expect(result, Right(tResponse));

        // Verify that a request was made with a current timestamp
        final captured =
            verify(mockRepository.updateInvoiceStatus(captureAny)).captured;
        final capturedRequest = captured.first as InvoiceStatusUpdateRequest;

        // Check that the timestamp is within a reasonable range (within 1 second)
        final timeDifference =
            capturedRequest.paymentDetails.paymentDate
                .difference(beforeCall)
                .inMilliseconds
                .abs();
        expect(
          timeDifference < 1000,
          isTrue,
          reason: 'Payment date should be within 1 second of call time',
        );
      },
    );
  });
}
