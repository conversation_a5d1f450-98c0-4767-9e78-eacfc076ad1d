import 'dart:convert';
import '../../../core/constants/app_constants.dart';
import '../../../core/error/exceptions.dart';
import '../../../core/network/api_client.dart';
import '../../../core/utils/logger.dart';
import '../../models/payments/payment_request_model.dart';
import '../../models/payments/payment_session_model.dart';
import '../../models/payments/payment_transaction_model.dart';

/// Abstract interface for payment remote data source
abstract class PaymentRemoteDataSource {
  /// Creates a payment session with Zoho payment gateway
  Future<PaymentSessionModel> createPaymentSession(PaymentRequestModel request);

  /// Gets payment status for a transaction
  Future<PaymentTransactionModel> getPaymentStatus(String transactionId);
}

/// Implementation of payment remote data source
class PaymentRemoteDataSourceImpl implements PaymentRemoteDataSource {
  final ApiClient apiClient;
  final AppLogger logger;

  PaymentRemoteDataSourceImpl({required this.apiClient, required this.logger});

  @override
  Future<PaymentSessionModel> createPaymentSession(
    PaymentRequestModel request,
  ) async {
    try {
      logger.i('Creating payment link for invoice: ${request.invoiceNumber}');

      // Prepare the request payload for the new Zoho API endpoint
      final requestPayload = {
        'amount': request.amount,
        'description': request.description,
        'customer_email': request.customerEmail ?? '<EMAIL>',
        'customer_name': request.customerName ?? 'AquaPartner Customer',
        'send_email': true,
        'meta_data': [
          {'key': 'invoice_number', 'value': request.invoiceNumber},
          {'key': 'customer_id', 'value': request.customerId},
          if (request.customerPhone != null)
            {'key': 'customer_phone', 'value': request.customerPhone!},
        ],
      };

      logger.d('Payment request payload: $requestPayload');

      // Use the new Zoho payment link creation endpoint
      final response = await apiClient.post(
        '${AppConstants.baseUrl}/zoho/payments/create-link',
        data: requestPayload,
      );

      logger.d('API response status: ${response.statusCode}');
      logger.d('API response data: ${response.data}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final jsonData = jsonDecode(response.data) as Map<String, dynamic>;

        if (jsonData['success'] == true) {
          logger.i('Payment link created successfully');
          logger.d('Parsed JSON data: $jsonData');

          try {
            final sessionModel = PaymentSessionModel.fromZohoLinkResponse(
              jsonData,
            );
            logger.i(
              'PaymentSessionModel created successfully with amount: ${sessionModel.amount}',
            );
            return sessionModel;
          } catch (parseError) {
            logger.e(
              'Error parsing payment link response: ${parseError.toString()}',
            );
            logger.e('JSON data that failed to parse: $jsonData');
            throw ServerException();
          }
        } else {
          final errorMessage = jsonData['message'] ?? 'Unknown error';
          logger.e('API returned error: $errorMessage');
          throw ServerException();
        }
      } else {
        logger.e('Failed to create payment link: ${response.statusCode}');
        logger.e('Error response: ${response.data}');
        throw ServerException();
      }
    } catch (e) {
      logger.e('Error creating payment link: ${e.toString()}');
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException();
    }
  }

  @override
  Future<PaymentTransactionModel> getPaymentStatus(String transactionId) async {
    try {
      logger.i('Getting payment status for transaction: $transactionId');

      final response = await apiClient.get(
        '${AppConstants.baseUrl}/zoho/payments/status/$transactionId',
      );

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.data) as Map<String, dynamic>;
        logger.i('Payment status retrieved successfully');
        return PaymentTransactionModel.fromJson(jsonData);
      } else {
        logger.e('Failed to get payment status: ${response.statusCode}');
        throw ServerException();
      }
    } catch (e) {
      logger.e('Error getting payment status: ${e.toString()}');
      throw ServerException();
    }
  }
}
