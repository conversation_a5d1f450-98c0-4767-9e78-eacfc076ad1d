import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/invoices/invoice_status_update_request.dart';
import '../../entities/invoices/invoice_status_update_response.dart';
import '../../repositories/invoice_status_repository.dart';

/// Use case for updating invoice status after successful payment
class UpdateInvoiceStatusUseCase implements UseCase<InvoiceStatusUpdateResponse, InvoiceStatusUpdateRequest> {
  final InvoiceStatusRepository repository;

  UpdateInvoiceStatusUseCase(this.repository);

  @override
  Future<Either<Failure, InvoiceStatusUpdateResponse>> call(
    InvoiceStatusUpdateRequest params,
  ) async {
    return await repository.updateInvoiceStatus(params);
  }

  /// Convenience method to update status from payment success data
  Future<Either<Failure, InvoiceStatusUpdateResponse>> updateFromPaymentSuccess({
    required String invoiceId,
    required double paymentAmount,
    required String? transactionId,
    DateTime? paymentDate,
  }) async {
    final request = InvoiceStatusUpdateRequest.fromPaymentSuccess(
      invoiceId: invoiceId,
      paymentAmount: paymentAmount,
      transactionId: transactionId,
      paymentDate: paymentDate,
    );

    return await call(request);
  }
}
