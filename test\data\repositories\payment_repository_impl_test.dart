import 'package:aquapartner/core/error/exceptions.dart';
import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/core/network/network_info.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/data/datasources/remote/payment_remote_data_source.dart';
import 'package:aquapartner/data/models/payments/payment_request_model.dart';
import 'package:aquapartner/data/models/payments/payment_session_model.dart';
import 'package:aquapartner/data/models/payments/payment_transaction_model.dart';
import 'package:aquapartner/data/repositories/payment_repository_impl.dart';
import 'package:aquapartner/domain/entities/payments/payment_request.dart';
import 'package:aquapartner/domain/entities/payments/payment_transaction.dart';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockPaymentRemoteDataSource extends Mock
    implements PaymentRemoteDataSource {}

class MockNetworkInfo extends Mock implements NetworkInfo {}

class MockAppLogger extends Mock implements AppLogger {}

void main() {
  group('PaymentRepositoryImpl', () {
    late PaymentRepositoryImpl repository;
    late MockPaymentRemoteDataSource mockRemoteDataSource;
    late MockNetworkInfo mockNetworkInfo;
    late MockAppLogger mockLogger;

    setUp(() {
      mockRemoteDataSource = MockPaymentRemoteDataSource();
      mockNetworkInfo = MockNetworkInfo();
      mockLogger = MockAppLogger();
      repository = PaymentRepositoryImpl(
        remoteDataSource: mockRemoteDataSource,
        networkInfo: mockNetworkInfo,
        logger: mockLogger,
      );
    });

    group('createPaymentSession', () {
      late PaymentRequest testRequest;
      late PaymentRequestModel testRequestModel;
      late PaymentSessionModel testSessionModel;

      setUp(() {
        testRequest = PaymentRequest(
          amount: 1500.0,
          currencyCode: 'INR',
          customerId: 'customer_123',
          invoiceNumber: 'INV001',
          description: 'Payment for AquaPartner Invoice INV001',
          customerName: 'Test Customer',
          customerEmail: '<EMAIL>',
          customerPhone: '+919999999999',
        );

        testRequestModel = PaymentRequestModel.fromEntity(testRequest);

        testSessionModel = PaymentSessionModel(
          sessionId: 'session_123',
          sessionUrl: 'https://payments.zoho.in/paymentlinks/test_token',
          paymentSessionId: 'session_123',
          currency: 'INR',
          amount: 1500.0,
          createdTime: DateTime.now().millisecondsSinceEpoch,
        );
      });

      test(
        'should return PaymentSession when network is connected and call succeeds',
        () async {
          // Arrange
          when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
          when(
            () => mockRemoteDataSource.createPaymentSession(any()),
          ).thenAnswer((_) async => testSessionModel);

          // Act
          final result = await repository.createPaymentSession(testRequest);

          // Assert
          expect(result, isA<Right<Failure, dynamic>>());
          result.fold((failure) => fail('Expected Right but got Left'), (
            session,
          ) {
            expect(session.sessionId, equals('session_123'));
            expect(
              session.sessionUrl,
              equals('https://payments.zoho.in/paymentlinks/test_token'),
            );
            expect(session.amount, equals(1500.0));
          });

          verify(() => mockNetworkInfo.isConnected).called(1);
          verify(
            () => mockRemoteDataSource.createPaymentSession(any()),
          ).called(1);
        },
      );

      test(
        'should return NetworkFailure when network is not connected',
        () async {
          // Arrange
          when(
            () => mockNetworkInfo.isConnected,
          ).thenAnswer((_) async => false);

          // Act
          final result = await repository.createPaymentSession(testRequest);

          // Assert
          expect(result, isA<Left<Failure, dynamic>>());
          result.fold(
            (failure) => expect(failure, isA<NetworkFailure>()),
            (session) => fail('Expected Left but got Right'),
          );

          verify(() => mockNetworkInfo.isConnected).called(1);
          verifyNever(() => mockRemoteDataSource.createPaymentSession(any()));
        },
      );

      test('should return ServerFailure when DioException occurs', () async {
        // Arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(() => mockRemoteDataSource.createPaymentSession(any())).thenThrow(
          DioException(
            requestOptions: RequestOptions(path: '/test'),
            type: DioExceptionType.connectionTimeout,
          ),
        );

        // Act
        final result = await repository.createPaymentSession(testRequest);

        // Assert
        expect(result, isA<Left<Failure, dynamic>>());
        result.fold(
          (failure) => expect(failure, isA<ServerFailure>()),
          (session) => fail('Expected Left but got Right'),
        );
      });

      test('should return ServerFailure when ServerException occurs', () async {
        // Arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockRemoteDataSource.createPaymentSession(any()),
        ).thenThrow(ServerException());

        // Act
        final result = await repository.createPaymentSession(testRequest);

        // Assert
        expect(result, isA<Left<Failure, dynamic>>());
        result.fold(
          (failure) => expect(failure, isA<ServerFailure>()),
          (session) => fail('Expected Left but got Right'),
        );
      });

      test('should return AuthFailure when AuthException occurs', () async {
        // Arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockRemoteDataSource.createPaymentSession(any()),
        ).thenThrow(AuthException());

        // Act
        final result = await repository.createPaymentSession(testRequest);

        // Assert
        expect(result, isA<Left<Failure, dynamic>>());
        result.fold(
          (failure) => expect(failure, isA<AuthFailure>()),
          (session) => fail('Expected Left but got Right'),
        );
      });

      test(
        'should return ServerFailure when unexpected exception occurs',
        () async {
          // Arrange
          when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
          when(
            () => mockRemoteDataSource.createPaymentSession(any()),
          ).thenThrow(Exception('Unexpected error'));

          // Act
          final result = await repository.createPaymentSession(testRequest);

          // Assert
          expect(result, isA<Left<Failure, dynamic>>());
          result.fold(
            (failure) => expect(failure, isA<ServerFailure>()),
            (session) => fail('Expected Left but got Right'),
          );
        },
      );
    });

    group('processPayment', () {
      test('should return mock PaymentTransaction when called', () async {
        // Arrange
        const sessionId = 'session_123';
        const invoiceNumber = 'INV001';
        const amount = 1500.0;
        const currency = 'INR';

        // Act
        final result = await repository.processPayment(
          sessionId: sessionId,
          invoiceNumber: invoiceNumber,
          amount: amount,
          currency: currency,
        );

        // Assert
        expect(result, isA<Right<Failure, PaymentTransaction>>());
        result.fold((failure) => fail('Expected Right but got Left'), (
          transaction,
        ) {
          expect(transaction.sessionId, equals(sessionId));
          expect(transaction.invoiceNumber, equals(invoiceNumber));
          expect(transaction.amount, equals(amount));
          expect(transaction.currency, equals(currency));
          expect(transaction.status, equals(PaymentStatus.pending));
          expect(transaction.transactionId, startsWith('TXN_'));
          expect(transaction.paymentId, startsWith('PAY_'));
        });
      });
    });

    group('getPaymentStatus', () {
      late PaymentTransactionModel testTransactionModel;

      setUp(() {
        testTransactionModel = PaymentTransactionModel(
          transactionId: 'txn_123',
          paymentId: 'pay_123',
          sessionId: 'session_123',
          invoiceNumber: 'INV001',
          amount: 1500.0,
          currency: 'INR',
          status: 'success',
          timestamp: DateTime.now(),
        );
      });

      test(
        'should return PaymentTransaction when network is connected and call succeeds',
        () async {
          // Arrange
          const transactionId = 'txn_123';
          when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
          when(
            () => mockRemoteDataSource.getPaymentStatus(transactionId),
          ).thenAnswer((_) async => testTransactionModel);

          // Act
          final result = await repository.getPaymentStatus(transactionId);

          // Assert
          expect(result, isA<Right<Failure, PaymentTransaction>>());
          result.fold((failure) => fail('Expected Right but got Left'), (
            transaction,
          ) {
            expect(transaction.transactionId, equals('txn_123'));
            expect(transaction.status, equals(PaymentStatus.success));
          });

          verify(() => mockNetworkInfo.isConnected).called(1);
          verify(
            () => mockRemoteDataSource.getPaymentStatus(transactionId),
          ).called(1);
        },
      );

      test(
        'should return NetworkFailure when network is not connected',
        () async {
          // Arrange
          const transactionId = 'txn_123';
          when(
            () => mockNetworkInfo.isConnected,
          ).thenAnswer((_) async => false);

          // Act
          final result = await repository.getPaymentStatus(transactionId);

          // Assert
          expect(result, isA<Left<Failure, PaymentTransaction>>());
          result.fold(
            (failure) => expect(failure, isA<NetworkFailure>()),
            (transaction) => fail('Expected Left but got Right'),
          );

          verify(() => mockNetworkInfo.isConnected).called(1);
          verifyNever(() => mockRemoteDataSource.getPaymentStatus(any()));
        },
      );

      test('should return ServerFailure when ServerException occurs', () async {
        // Arrange
        const transactionId = 'txn_123';
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockRemoteDataSource.getPaymentStatus(transactionId),
        ).thenThrow(ServerException());

        // Act
        final result = await repository.getPaymentStatus(transactionId);

        // Assert
        expect(result, isA<Left<Failure, PaymentTransaction>>());
        result.fold(
          (failure) => expect(failure, isA<ServerFailure>()),
          (transaction) => fail('Expected Left but got Right'),
        );
      });
    });
  });
}
