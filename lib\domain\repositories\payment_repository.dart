import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/payments/payment_request.dart';
import '../entities/payments/payment_session.dart';
import '../entities/payments/payment_transaction.dart';

/// Repository interface for payment operations
abstract class PaymentRepository {
  /// Creates a payment session with Zoho payment gateway
  /// 
  /// Returns [PaymentSession] on success or [Failure] on error
  Future<Either<Failure, PaymentSession>> createPaymentSession(
    PaymentRequest request,
  );

  /// Processes a payment transaction
  /// 
  /// Returns [PaymentTransaction] on success or [Failure] on error
  Future<Either<Failure, PaymentTransaction>> processPayment({
    required String sessionId,
    required String invoiceNumber,
    required double amount,
    required String currency,
  });

  /// Gets the payment status for a transaction
  /// 
  /// Returns [PaymentTransaction] on success or [Failure] on error
  Future<Either<Failure, PaymentTransaction>> getPaymentStatus(
    String transactionId,
  );
}
