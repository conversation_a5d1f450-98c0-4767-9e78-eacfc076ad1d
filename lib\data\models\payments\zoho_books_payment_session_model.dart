import '../../../domain/entities/payments/zoho_books_payment_session.dart';

/// Data model for Zoho Books payment session
class ZohoBooksPaymentSessionModel {
  final String invoiceId;
  final String paymentLinkUrl;
  final double amount;
  final String currency;
  final String description;
  final DateTime createdAt;

  ZohoBooksPaymentSessionModel({
    required this.invoiceId,
    required this.paymentLinkUrl,
    required this.amount,
    required this.currency,
    required this.description,
    required this.createdAt,
  });

  /// Creates ZohoBooksPaymentSessionModel from JSON response
  factory ZohoBooksPaymentSessionModel.fromJson(Map<String, dynamic> json) {
    return ZohoBooksPaymentSessionModel(
      invoiceId: json['invoice_id']?.toString() ?? '',
      paymentLinkUrl: json['payment_link_url']?.toString() ?? '',
      amount: (json['amount'] ?? 0.0).toDouble(),
      currency: json['currency']?.toString() ?? 'INR',
      description: json['description']?.toString() ?? '',
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : DateTime.now(),
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'invoice_id': invoiceId,
      'payment_link_url': paymentLinkUrl,
      'amount': amount,
      'currency': currency,
      'description': description,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// Converts to domain entity
  ZohoBooksPaymentSession toEntity() {
    return ZohoBooksPaymentSession(
      invoiceId: invoiceId,
      paymentLinkUrl: paymentLinkUrl,
      amount: amount,
      currency: currency,
      description: description,
      createdAt: createdAt,
    );
  }
}
