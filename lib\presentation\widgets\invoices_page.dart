import 'package:aqua_ui/aqua_ui.dart';
import 'package:aquapartner/core/mixins/analytics_mixin.dart';
import 'package:aquapartner/core/utils/currency_formatter.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/injection_container.dart' as di;
import 'package:aquapartner/presentation/widgets/loading_widget.dart';
import 'package:aquapartner/presentation/widgets/styled_generic_table.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../domain/entities/invoices/invoice.dart';
import '../../domain/entities/payments/payment_session.dart';
import '../../domain/usecases/invoices/update_invoice_status_use_case.dart';
import '../cubit/auth/auth_cubit.dart';
import '../cubit/auth/auth_state.dart';
import '../cubit/invoices/invoices_cubit.dart';
import '../cubit/invoices/invoices_state.dart';
import '../cubit/payments/payment_cubit.dart';
import '../cubit/payments/payment_state.dart';
import 'payment/payment_dialog.dart';

class InvoicesPage extends StatefulWidget {
  const InvoicesPage({super.key});

  @override
  State<InvoicesPage> createState() => _InvoicesPageState();
}

class _InvoicesPageState extends State<InvoicesPage> with AnalyticsMixin {
  @override
  String get screenName => 'InvoicesPage';

  // Track screen view time
  DateTime? _screenViewStartTime;
  DateTime? _lastInteractionTime;

  // Track user interactions
  int _invoiceTapCount = 0;
  int _scrollCount = 0;

  // Track scroll position
  final ScrollController _scrollController = ScrollController();

  // Track payment operations
  String? _processingPaymentInvoiceId;

  @override
  void initState() {
    super.initState();
    _screenViewStartTime = DateTime.now();
    _lastInteractionTime = _screenViewStartTime;

    // Add scroll listener for analytics
    _scrollController.addListener(_onScroll);

    context.read<InvoicesCubit>().loadInvoices();

    // Track page initialization
    trackEvent('invoices_page_initialized');
  }

  @override
  void dispose() {
    // Track total time spent on screen
    _trackScreenDuration();

    // Track final engagement metrics
    _trackEngagementMetrics();

    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _trackScreenDuration() {
    if (_screenViewStartTime != null) {
      final duration = DateTime.now().difference(_screenViewStartTime!);
      trackEvent(
        'invoices_screen_duration',
        params: {
          'duration_ms': duration.inMilliseconds.toString(),
          'duration_seconds': duration.inSeconds.toString(),
        },
      );
    }
  }

  void _trackEngagementMetrics() {
    trackEvent(
      'invoices_engagement',
      params: {
        'invoice_tap_count': _invoiceTapCount.toString(),
        'scroll_count': _scrollCount.toString(),
        'time_since_last_interaction':
            _lastInteractionTime != null
                ? DateTime.now()
                    .difference(_lastInteractionTime!)
                    .inSeconds
                    .toString()
                : '0',
      },
    );
  }

  void _onScroll() {
    // Don't track every tiny scroll event - only significant ones
    if (!_scrollController.hasClients) return;

    // Track scroll position as percentage
    final scrollPosition = _scrollController.position.pixels;
    final maxScroll = _scrollController.position.maxScrollExtent;

    if (maxScroll <= 0) return;

    final scrollPercentage = (scrollPosition / maxScroll * 100).round();

    // Only track at 25%, 50%, 75%, and 100% scroll positions
    if (scrollPercentage == 25 ||
        scrollPercentage == 50 ||
        scrollPercentage == 75 ||
        scrollPercentage == 100) {
      _scrollCount++;
      _lastInteractionTime = DateTime.now();

      trackEvent(
        'invoices_scrolled',
        params: {
          'scroll_percentage': scrollPercentage.toString(),
          'scroll_position': scrollPosition.toString(),
        },
      );
    }
  }

  void _trackInvoiceTap(Invoice invoice) {
    _invoiceTapCount++;
    _lastInteractionTime = DateTime.now();

    trackUserInteraction(
      'view_invoice_details',
      'table_row',
      elementId: invoice.invoiceNumber,
      additionalParams: {
        'invoice_number': invoice.invoiceNumber,
        'invoice_date': invoice.invoiceDate.toIso8601String(),
        'invoice_amount': invoice.total.toString(),
        'invoice_status': invoice.invoiceStatus,
        'invoice_tap_count': _invoiceTapCount.toString(),
        'time_on_screen_before_tap':
            _screenViewStartTime != null
                ? DateTime.now()
                    .difference(_screenViewStartTime!)
                    .inSeconds
                    .toString()
                : '0',
      },
    );
  }

  /// Handles Pay Now button tap for overdue invoices
  Future<void> _handlePayNow(Invoice invoice) async {
    try {
      setState(() {
        _processingPaymentInvoiceId = invoice.invoiceId;
      });

      // Track payment initiation
      trackEvent(
        'pay_now_button_tapped',
        params: {
          'invoice_number': invoice.invoiceNumber,
          'invoice_amount': invoice.total.toString(),
          'invoice_status': invoice.invoiceStatus,
          'payment_method': 'zoho_books', // Track that this is using Zoho Books
        },
      );

      // Track direct payment initiation (bypassing confirmation)
      trackEvent(
        'zoho_books_payment_direct_initiation',
        params: {
          'invoice_number': invoice.invoiceNumber,
          'invoice_amount': invoice.total.toString(),
          'bypass_confirmation': 'true',
          'payment_provider': 'zoho_books',
        },
      );

      // Create Zoho Books payment link directly using PaymentCubit (bypassing confirmation dialog)
      if (mounted) {
        context.read<PaymentCubit>().createZohoBooksPaymentLink(invoice);
      }
    } catch (e) {
      setState(() {
        _processingPaymentInvoiceId = null;
      });

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to initiate payment: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Handles payment session creation result
  void _handlePaymentSessionResult(PaymentState state) {
    if (state is PaymentSessionCreated) {
      // Show payment WebView for regular Zoho payments (legacy support)
      _showPaymentWebView(state.session);
    } else if (state is ZohoBooksPaymentLinkCreated) {
      // Show payment WebView for Zoho Books payments
      _showZohoBooksPaymentWebView(state.session);
    } else if (state is PaymentFailure) {
      setState(() {
        _processingPaymentInvoiceId = null;
      });

      // Show error dialog
      PaymentDialog.showResult(
        context: context,
        isSuccess: false,
        invoiceNumber: state.invoiceNumber ?? 'Unknown',
        errorMessage: state.message,
      );
    } else if (state is ZohoBooksPaymentFailure) {
      setState(() {
        _processingPaymentInvoiceId = null;
      });

      // Show error dialog for Zoho Books payment failure
      PaymentDialog.showResult(
        context: context,
        isSuccess: false,
        invoiceNumber: state.invoiceId ?? 'Unknown',
        errorMessage: state.message,
      );
    }
  }

  /// Shows the payment WebView for regular Zoho payments
  Future<void> _showPaymentWebView(paymentSession) async {
    try {
      // Get customer information for payment
      String? customerName;
      String? customerEmail;
      String? invoiceNumber;

      // Try to get customer info from auth service
      final authState = context.read<AuthCubit>().state;
      if (authState is AuthSuccess) {
        // Use phone number as fallback for customer name
        customerName = authState.user.phoneNumber;
        customerEmail = '<EMAIL>'; // Default email
      }

      // Extract invoice number from payment session or use a default
      invoiceNumber = paymentSession.paymentSessionId;

      final result = await PaymentDialog.show(
        context: context,
        paymentSession: paymentSession,
        customerName: customerName,
        customerEmail: customerEmail,
        invoiceNumber: invoiceNumber,
      );

      setState(() {
        _processingPaymentInvoiceId = null;
      });

      if (result != null && mounted) {
        // Show payment result
        await PaymentDialog.showResult(
          context: context,
          isSuccess: result['isSuccess'] ?? false,
          invoiceNumber: paymentSession.paymentSessionId,
          transactionId: result['transactionId'],
          errorMessage: result['errorMessage'],
        );

        // If payment was successful, update invoice status and refresh invoices
        if (result['isSuccess'] == true && mounted) {
          await _handlePaymentSuccess(
            invoiceId: paymentSession.paymentSessionId,
            paymentAmount: paymentSession.amount,
            transactionId: result['transactionId'],
          );
        }
      }
    } catch (e) {
      setState(() {
        _processingPaymentInvoiceId = null;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Payment error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Shows the payment WebView for Zoho Books payments
  Future<void> _showZohoBooksPaymentWebView(zohoBooksSession) async {
    try {
      // Get customer information for payment
      String? customerName;
      String? customerEmail;
      String? invoiceNumber;

      // Try to get customer info from auth service
      final authState = context.read<AuthCubit>().state;
      if (authState is AuthSuccess) {
        // Use phone number as fallback for customer name
        customerName = authState.user.phoneNumber;
        customerEmail = '<EMAIL>'; // Default email
      }

      // Extract invoice number from Zoho Books session
      invoiceNumber = zohoBooksSession.invoiceId;

      // Create a compatible payment session object for the existing PaymentDialog
      final compatiblePaymentSession = _createCompatiblePaymentSession(
        zohoBooksSession,
      );

      final result = await PaymentDialog.show(
        context: context,
        paymentSession: compatiblePaymentSession,
        customerName: customerName,
        customerEmail: customerEmail,
        invoiceNumber: invoiceNumber,
      );

      setState(() {
        _processingPaymentInvoiceId = null;
      });

      if (result != null && mounted) {
        // Show payment result
        await PaymentDialog.showResult(
          context: context,
          isSuccess: result['isSuccess'] ?? false,
          invoiceNumber: zohoBooksSession.invoiceId,
          transactionId: result['transactionId'],
          errorMessage: result['errorMessage'],
        );

        // If payment was successful, update invoice status and refresh invoices
        if (result['isSuccess'] == true && mounted) {
          await _handlePaymentSuccess(
            invoiceId: zohoBooksSession.invoiceId,
            paymentAmount: zohoBooksSession.amount,
            transactionId: result['transactionId'],
          );
        }
      }
    } catch (e) {
      setState(() {
        _processingPaymentInvoiceId = null;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Payment error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Creates a compatible payment session object from Zoho Books session
  PaymentSession _createCompatiblePaymentSession(zohoBooksSession) {
    // Create a payment session that's compatible with the existing PaymentDialog
    // This allows us to reuse the existing WebView component
    return PaymentSession(
      sessionId: 'zoho_books_${zohoBooksSession.invoiceId}',
      sessionUrl: zohoBooksSession.paymentLinkUrl,
      paymentSessionId: zohoBooksSession.invoiceId,
      amount: zohoBooksSession.amount,
      currency: zohoBooksSession.currency,
      createdTime: zohoBooksSession.createdAt.millisecondsSinceEpoch,
      metaData: const [],
    );
  }

  /// Handles successful payment by updating invoice status and refreshing the list
  Future<void> _handlePaymentSuccess({
    required String invoiceId,
    required double paymentAmount,
    required String? transactionId,
  }) async {
    try {
      // Track payment success
      trackEvent(
        'payment_success_processing',
        params: {
          'invoice_id': invoiceId,
          'payment_amount': paymentAmount.toString(),
          'transaction_id': transactionId ?? 'unknown',
        },
      );

      // Get the use case from dependency injection
      final updateInvoiceStatusUseCase =
          context.read<UpdateInvoiceStatusUseCase>();

      // Update invoice status in the backend
      final result = await updateInvoiceStatusUseCase.updateFromPaymentSuccess(
        invoiceId: invoiceId,
        paymentAmount: paymentAmount,
        transactionId: transactionId,
      );

      result.fold(
        (failure) {
          // Log the failure but don't show error to user (payment was successful)
          final logger = di.sl<AppLogger>();
          logger.e('Failed to update invoice status after successful payment');
          logger.e('Invoice ID: $invoiceId');
          logger.e('Failure: ${failure.runtimeType}');

          // Track the failure for analytics
          trackEvent(
            'invoice_status_update_failed',
            params: {
              'invoice_id': invoiceId,
              'payment_amount': paymentAmount.toString(),
              'failure_type': failure.runtimeType.toString(),
            },
          );

          // Still refresh invoices to show any changes
          if (mounted) {
            context.read<InvoicesCubit>().loadInvoices();
          }
        },
        (response) {
          // Success - invoice status updated
          final logger = di.sl<AppLogger>();
          logger.i('Invoice status updated successfully');
          logger.i('Invoice ID: ${response.invoiceId}');
          logger.i('Previous Status: ${response.previousStatus}');
          logger.i('Current Status: ${response.newStatus}');

          // Track successful status update
          trackEvent(
            'invoice_status_updated_successfully',
            params: {
              'invoice_id': response.invoiceId,
              'previous_status': response.previousStatus,
              'current_status': response.newStatus,
              'payment_amount': paymentAmount.toString(),
            },
          );

          // Refresh invoices to show updated status
          if (mounted) {
            context.read<InvoicesCubit>().loadInvoices();
          }
        },
      );
    } catch (e) {
      // Log unexpected errors but don't show to user
      final logger = di.sl<AppLogger>();
      logger.e(
        'Unexpected error during payment success handling: ${e.toString()}',
      );

      // Track the error
      trackEvent(
        'payment_success_handling_error',
        params: {'invoice_id': invoiceId, 'error': e.toString()},
      );

      // Still refresh invoices
      if (mounted) {
        context.read<InvoicesCubit>().loadInvoices();
      }
    }
  }

  /// Builds the action button for each invoice row
  Widget _buildActionButton(Invoice invoice) {
    final isOverdue = invoice.invoiceStatus == "Overdue";
    final isProcessing = _processingPaymentInvoiceId == invoice.invoiceId;

    if (!isOverdue) {
      // Show status text for non-overdue invoices
      return AquaText.caption(
        invoice.invoiceStatus == 'Closed' || invoice.invoiceStatus == 'Paid'
            ? 'Paid'
            : 'No payment required',
        color: Colors.grey[600] ?? Colors.grey,
      );
    }

    // Show Pay Now button for overdue invoices
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: acPrimaryBlue,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        minimumSize: Size(80, 32),
      ),
      onPressed: isProcessing ? null : () => _handlePayNow(invoice),
      child:
          isProcessing
              ? Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    width: 12,
                    height: 12,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  SizedBox(width: 8),
                  AquaText.caption(
                    'Processing...',
                    color: Colors.white,
                    weight: AquaFontWeight.semibold,
                  ),
                ],
              )
              : AquaText.caption(
                'Pay Now',
                color: Colors.white,
                weight: AquaFontWeight.semibold,
              ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<PaymentCubit, PaymentState>(
      listener: (context, state) {
        // Handle both regular Zoho and Zoho Books payment states
        _handlePaymentSessionResult(state);

        // Reset processing state when payment is loading
        if (state is PaymentSessionLoading ||
            state is ZohoBooksPaymentLinkLoading) {
          // Keep the loading indicator visible
        }
      },
      child: BlocConsumer<InvoicesCubit, InvoicesState>(
        builder: (context, state) {
          if (state is InvoicesLoaded) {
            return state.invoices.isNotEmpty
                ? SingleChildScrollView(
                  controller: _scrollController,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 16),
                        StyledGenericTable<Invoice>(
                          items: state.invoices,
                          showDividers: true,
                          onRowTap: (invoice) {
                            _trackInvoiceTap(invoice);
                            // Add navigation logic here if needed
                          },
                          columns: [
                            ColumnConfig<Invoice>(
                              title: 'Date',
                              width: 100,
                              cellBuilder:
                                  (invoice) => Align(
                                    alignment: Alignment.center,
                                    child: AquaText.body(
                                      DateFormat(
                                        'dd-MM-yyyy',
                                      ).format(invoice.invoiceDate),
                                    ),
                                  ),
                            ),
                            ColumnConfig<Invoice>(
                              title: 'Total',
                              width: 100,
                              titleAlignment: Alignment.center,
                              bodyAlignment: Alignment.centerRight,
                              cellBuilder:
                                  (invoice) => AquaText.body(
                                    CurrencyFormatter.formatAsINR(
                                      invoice.total,
                                      decimalPlaces: 0,
                                    ),
                                    weight: AquaFontWeight.bold,
                                  ),
                            ),
                            ColumnConfig<Invoice>(
                              title: 'Invoice Number',
                              width: 150,
                              cellBuilder:
                                  (invoice) => AquaText.body(
                                    invoice.invoiceNumber,
                                    weight: AquaFontWeight.semibold,
                                    color: acPrimaryBlue,
                                  ),
                            ),
                            ColumnConfig<Invoice>(
                              title: 'Status',
                              width: 100,
                              cellBuilder:
                                  (invoice) =>
                                      AquaText.body(invoice.invoiceStatus),
                            ),
                            ColumnConfig<Invoice>(
                              title: 'Action',
                              width: 120,
                              cellBuilder:
                                  (invoice) => _buildActionButton(invoice),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                )
                : Center(
                  child: AquaText.subheadline("You dont have any invoices"),
                );
          }
          return LoadingWidget(message: "Please wait, your invoices loading.");
        },
        listener: (context, state) {
          // Track state changes for analytics
          if (state is InvoicesLoading) {
            trackEvent('invoices_loading');
          } else if (state is InvoicesLoaded) {
            _lastInteractionTime =
                DateTime.now(); // Update interaction time when data loads
            trackEvent(
              'invoices_loaded',
              params: {
                'invoice_count': state.invoices.length.toString(),
                'is_from_cache': state.isFromCache ? 'true' : 'false',
                'time_to_load':
                    _screenViewStartTime != null
                        ? DateTime.now()
                            .difference(_screenViewStartTime!)
                            .inMilliseconds
                            .toString()
                        : '0',
              },
            );
          } else if (state is InvoiceError) {
            trackEvent(
              'invoices_error',
              params: {'error_message': state.message},
            );
          }
        },
      ),
    );
  }
}
