import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../core/network/network_info.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/payments/zoho_books_payment_request.dart';
import '../../domain/entities/payments/zoho_books_payment_session.dart';
import '../../domain/repositories/zoho_books_payment_repository.dart';
import '../datasources/remote/zoho_books_payment_remote_data_source.dart';
import '../models/payments/zoho_books_payment_request_model.dart';

/// Implementation of Zoho Books payment repository
class ZohoBooksPaymentRepositoryImpl implements ZohoBooksPaymentRepository {
  final ZohoBooksPaymentRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;
  final AppLogger logger;

  ZohoBooksPaymentRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
    required this.logger,
  });

  @override
  Future<Either<Failure, ZohoBooksPaymentSession>> createPaymentLink(
    ZohoBooksPaymentRequest request,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final requestModel = ZohoBooksPaymentRequestModel.fromEntity(request);
        final sessionModel = await remoteDataSource.createPaymentLink(requestModel);
        return Right(sessionModel.toEntity());
      } on DioException catch (e) {
        logger.e('DioException in createPaymentLink: ${e.message}');
        return Left(ServerFailure());
      } on ServerException {
        logger.e('ServerException in createPaymentLink');
        return Left(ServerFailure());
      } on AuthException {
        logger.e('AuthException in createPaymentLink');
        return Left(AuthFailure());
      } catch (e) {
        logger.e('Unexpected error in createPaymentLink: ${e.toString()}');
        return Left(ServerFailure());
      }
    } else {
      logger.w('No network connection for createPaymentLink');
      return Left(NetworkFailure());
    }
  }
}
