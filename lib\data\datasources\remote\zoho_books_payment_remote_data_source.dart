import 'dart:convert';
import 'package:dio/dio.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/error/exceptions.dart';
import '../../../core/network/api_client.dart';
import '../../../core/utils/logger.dart';
import '../../models/payments/zoho_books_payment_request_model.dart';
import '../../models/payments/zoho_books_payment_session_model.dart';

/// Abstract interface for Zoho Books payment remote data source
abstract class ZohoBooksPaymentRemoteDataSource {
  /// Creates a payment link with Zoho Books payment gateway
  Future<ZohoBooksPaymentSessionModel> createPaymentLink(
    ZohoBooksPaymentRequestModel request,
  );
}

/// Implementation of Zoho Books payment remote data source
class ZohoBooksPaymentRemoteDataSourceImpl
    implements ZohoBooksPaymentRemoteDataSource {
  final ApiClient apiClient;
  final AppLogger logger;

  ZohoBooksPaymentRemoteDataSourceImpl({
    required this.apiClient,
    required this.logger,
  });

  @override
  Future<ZohoBooksPaymentSessionModel> createPaymentLink(
    ZohoBooksPaymentRequestModel request,
  ) async {
    try {
      logger.i(
        'Creating Zoho Books payment link for invoice: ${request.invoiceId}',
      );

      // Prepare the request payload for Zoho Books API
      final requestPayload = request.toJson();

      logger.d('Zoho Books payment request payload: $requestPayload');
      logger.d('Invoice type analysis:');
      logger.d('  - Contains "ADJ": ${request.invoiceId.contains("ADJ")}');
      logger.d('  - Contains "CR": ${request.invoiceId.contains("CR")}');
      logger.d('  - Contains "DR": ${request.invoiceId.contains("DR")}');
      logger.d(
        '  - Is adjustment/credit invoice: ${request.invoiceId.contains("ADJ") || request.invoiceId.contains("CR") || request.invoiceId.contains("DR")}',
      );

      // Call the Zoho Books payment link creation endpoint
      final response = await apiClient.post(
        '${AppConstants.baseUrl}/zoho-books/payments/create-link',
        data: requestPayload,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        try {
          final jsonData = jsonDecode(response.data) as Map<String, dynamic>;
          logger.i(
            'Zoho Books payment link created successfully with status ${response.statusCode}',
          );
          logger.d('Response data: $jsonData');

          // Try to extract payment URL from different possible response structures
          String? paymentLinkUrl;

          // Check for direct payment_link_url field
          if (jsonData.containsKey('payment_link_url') &&
              jsonData['payment_link_url'] != null) {
            paymentLinkUrl = jsonData['payment_link_url'].toString();
          }
          // Check for alternative field names
          else if (jsonData.containsKey('paymentLinkUrl') &&
              jsonData['paymentLinkUrl'] != null) {
            paymentLinkUrl = jsonData['paymentLinkUrl'].toString();
          }
          // Check for nested structure (e.g., data.payment_link_url)
          else if (jsonData.containsKey('data') && jsonData['data'] is Map) {
            final dataMap = jsonData['data'] as Map<String, dynamic>;
            if (dataMap.containsKey('payment_link_url') &&
                dataMap['payment_link_url'] != null) {
              paymentLinkUrl = dataMap['payment_link_url'].toString();
            }
          }
          // Check for payment_link field (alternative naming)
          else if (jsonData.containsKey('payment_link') &&
              jsonData['payment_link'] != null) {
            paymentLinkUrl = jsonData['payment_link'].toString();
          }

          if (paymentLinkUrl == null || paymentLinkUrl.isEmpty) {
            logger.e('API response missing or empty payment URL field');
            logger.e('Available fields: ${jsonData.keys.toList()}');
            logger.e('Full response: $jsonData');

            // Log potential nested structures
            jsonData.forEach((key, value) {
              if (value is Map) {
                logger.e('Nested object "$key": ${value.keys.toList()}');
              }
            });

            throw ServerException();
          }

          logger.i('Successfully extracted payment_link_url: $paymentLinkUrl');

          // Create session model with the response data
          final sessionData = {
            'invoice_id': request.invoiceId,
            'payment_link_url': paymentLinkUrl,
            'amount': request.amount,
            'currency': request.currency,
            'description': request.description,
            'created_at': DateTime.now().toIso8601String(),
          };

          return ZohoBooksPaymentSessionModel.fromJson(sessionData);
        } catch (e) {
          logger.e('Error parsing successful API response: ${e.toString()}');
          logger.e('Raw response data: ${response.data}');
          logger.e('Response type: ${response.data.runtimeType}');
          throw ServerException();
        }
      } else {
        // Enhanced error logging for debugging
        logger.e('Failed to create Zoho Books payment link:');
        logger.e('  - Status Code: ${response.statusCode}');
        logger.e('  - Response Headers: ${response.headers}');
        logger.e('  - Response Data: ${response.data}');
        logger.e(
          '  - Request URL: ${AppConstants.baseUrl}/zoho-books/payments/create-link',
        );
        logger.e('  - Request Payload: $requestPayload');

        // Try to parse error response if it's JSON
        try {
          if (response.data != null) {
            final errorData = jsonDecode(response.data) as Map<String, dynamic>;
            logger.e('  - Parsed Error: $errorData');
            if (errorData.containsKey('error')) {
              logger.e('  - Error Message: ${errorData['error']}');
            }
            if (errorData.containsKey('message')) {
              logger.e('  - API Message: ${errorData['message']}');
            }
          }
        } catch (parseError) {
          logger.e('  - Could not parse error response: $parseError');
        }

        throw ServerException();
      }
    } catch (e) {
      logger.e('Error creating Zoho Books payment link: ${e.toString()}');
      logger.e('Error type: ${e.runtimeType}');

      // Enhanced error logging for different exception types
      if (e is DioException) {
        logger.e('DioException details:');
        logger.e('  - Type: ${e.type}');
        logger.e('  - Message: ${e.message}');
        logger.e('  - Response: ${e.response?.data}');
        logger.e('  - Status Code: ${e.response?.statusCode}');
        logger.e('  - Request URL: ${e.requestOptions.path}');
        logger.e('  - Request Data: ${e.requestOptions.data}');
      }

      if (e is ServerException) {
        rethrow;
      }
      throw ServerException();
    }
  }
}
