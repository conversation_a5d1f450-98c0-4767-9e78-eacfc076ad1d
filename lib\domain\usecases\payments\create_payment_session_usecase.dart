import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../entities/payments/payment_request.dart';
import '../../entities/payments/payment_session.dart';
import '../../repositories/payment_repository.dart';

/// Use case for creating a payment session with Zoho payment gateway
class CreatePaymentSessionUseCase {
  final PaymentRepository repository;

  CreatePaymentSessionUseCase(this.repository);

  /// Creates a payment session for the given payment request
  /// 
  /// Returns [PaymentSession] on success or [Failure] on error
  Future<Either<Failure, PaymentSession>> call(PaymentRequest request) async {
    // Validate payment request
    if (request.amount <= 0) {
      return Left(ValidationFailure('Amount must be greater than zero'));
    }

    if (request.customerId.isEmpty) {
      return Left(ValidationFailure('Customer ID is required'));
    }

    if (request.invoiceNumber.isEmpty) {
      return Left(ValidationFailure('Invoice number is required'));
    }

    if (request.description.isEmpty) {
      return Left(ValidationFailure('Description is required'));
    }

    if (request.currencyCode.isEmpty) {
      return Left(ValidationFailure('Currency code is required'));
    }

    // Call repository to create payment session
    return await repository.createPaymentSession(request);
  }
}
