import 'dart:convert';

import 'package:aquapartner/core/constants/app_constants.dart';
import 'package:aquapartner/core/error/exceptions.dart';
import 'package:aquapartner/core/network/api_client.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/data/datasources/remote/payment_remote_data_source.dart';
import 'package:aquapartner/data/models/payments/payment_request_model.dart';
import 'package:aquapartner/data/models/payments/payment_session_model.dart';
import 'package:aquapartner/data/models/payments/payment_transaction_model.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockApiClient extends Mock implements ApiClient {}

class MockAppLogger extends Mock implements AppLogger {}

class MockResponse extends Mock implements Response<dynamic> {}

void main() {
  group('PaymentRemoteDataSourceImpl', () {
    late PaymentRemoteDataSourceImpl dataSource;
    late MockApiClient mockApiClient;
    late MockAppLogger mockLogger;

    setUp(() {
      mockApiClient = MockApiClient();
      mockLogger = MockAppLogger();
      dataSource = PaymentRemoteDataSourceImpl(
        apiClient: mockApiClient,
        logger: mockLogger,
      );
    });

    group('createPaymentSession', () {
      late PaymentRequestModel testRequest;
      late Map<String, dynamic> testResponseData;
      late MockResponse mockResponse;

      setUp(() {
        testRequest = PaymentRequestModel(
          amount: 1500.0,
          currencyCode: 'INR',
          customerId: 'customer_123',
          invoiceNumber: 'INV001',
          description: 'Payment for AquaPartner Invoice INV001',
          customerName: 'Test Customer',
          customerEmail: '<EMAIL>',
          customerPhone: '+919999999999',
        );

        testResponseData = {
          'success': true,
          'message': 'Payment link created successfully',
          'data': {
            'payment_link_id': '5619000000248086',
            'amount': '1500.00',
            'currency': 'INR',
            'description': 'Payment for AquaPartner Invoice INV001',
            'status': 'active',
            'created_time': 1751043827,
            'expires_at': '2025-07-27',
            'transaction_id': '685ecef3e19640cfcac11ebf',
          },
          'payment_link': {
            'url': 'https://payments.zoho.in/paymentlinks/test_token',
            'expires_at': '2025-07-27',
            'amount': '1500.00',
            'currency': 'INR',
            'status': 'active',
          },
        };

        mockResponse = MockResponse();
        when(() => mockResponse.statusCode).thenReturn(200);
        when(() => mockResponse.data).thenReturn(jsonEncode(testResponseData));
      });

      test('should create payment session successfully', () async {
        // Arrange
        when(
          () => mockApiClient.post(
            '${AppConstants.baseUrl}/zoho/payments/create-link',
            data: any(named: 'data'),
          ),
        ).thenAnswer((_) async => mockResponse);

        // Act
        final result = await dataSource.createPaymentSession(testRequest);

        // Assert
        expect(result, isA<PaymentSessionModel>());
        expect(result.sessionId, equals('5619000000248086'));
        expect(
          result.sessionUrl,
          equals('https://payments.zoho.in/paymentlinks/test_token'),
        );
        expect(result.amount, equals(1500.0));
        expect(result.currency, equals('INR'));

        verify(
          () => mockApiClient.post(
            '${AppConstants.baseUrl}/zoho/payments/create-link',
            data: {
              'amount': 1500.0,
              'description': 'Payment for AquaPartner Invoice INV001',
              'customer_email': '<EMAIL>',
              'customer_name': 'Test Customer',
              'send_email': true,
              'meta_data': [
                {'key': 'invoice_number', 'value': 'INV001'},
                {'key': 'customer_id', 'value': 'customer_123'},
                {'key': 'customer_phone', 'value': '+919999999999'},
              ],
            },
          ),
        ).called(1);
      });

      test('should handle API error response', () async {
        // Arrange
        final errorResponseData = {
          'success': false,
          'message': 'Invalid request parameters',
        };
        when(() => mockResponse.statusCode).thenReturn(400);
        when(() => mockResponse.data).thenReturn(jsonEncode(errorResponseData));
        when(
          () => mockApiClient.post(any(), data: any(named: 'data')),
        ).thenAnswer((_) async => mockResponse);

        // Act & Assert
        expect(
          () => dataSource.createPaymentSession(testRequest),
          throwsA(isA<ServerException>()),
        );
      });

      test('should handle network error', () async {
        // Arrange
        when(
          () => mockApiClient.post(any(), data: any(named: 'data')),
        ).thenThrow(
          DioException(
            requestOptions: RequestOptions(path: '/test'),
            type: DioExceptionType.connectionTimeout,
          ),
        );

        // Act & Assert
        expect(
          () => dataSource.createPaymentSession(testRequest),
          throwsA(isA<ServerException>()),
        );
      });

      test('should handle success false in response', () async {
        // Arrange
        final failureResponseData = {
          'success': false,
          'message': 'Payment link creation failed',
        };
        when(() => mockResponse.statusCode).thenReturn(200);
        when(
          () => mockResponse.data,
        ).thenReturn(jsonEncode(failureResponseData));
        when(
          () => mockApiClient.post(any(), data: any(named: 'data')),
        ).thenAnswer((_) async => mockResponse);

        // Act & Assert
        expect(
          () => dataSource.createPaymentSession(testRequest),
          throwsA(isA<ServerException>()),
        );
      });

      test('should use default values for missing customer info', () async {
        // Arrange
        final requestWithoutCustomerInfo = PaymentRequestModel(
          amount: 1500.0,
          currencyCode: 'INR',
          customerId: 'customer_123',
          invoiceNumber: 'INV001',
          description: 'Payment for AquaPartner Invoice INV001',
        );

        when(
          () => mockApiClient.post(
            '${AppConstants.baseUrl}/zoho/payments/create-link',
            data: any(named: 'data'),
          ),
        ).thenAnswer((_) async => mockResponse);

        // Act
        await dataSource.createPaymentSession(requestWithoutCustomerInfo);

        // Assert
        verify(
          () => mockApiClient.post(
            '${AppConstants.baseUrl}/zoho/payments/create-link',
            data: {
              'amount': 1500.0,
              'description': 'Payment for AquaPartner Invoice INV001',
              'customer_email': '<EMAIL>',
              'customer_name': 'AquaPartner Customer',
              'send_email': true,
              'meta_data': [
                {'key': 'invoice_number', 'value': 'INV001'},
                {'key': 'customer_id', 'value': 'customer_123'},
              ],
            },
          ),
        ).called(1);
      });
    });

    group('getPaymentStatus', () {
      test('should get payment status successfully', () async {
        // Arrange
        final testTransactionId = 'txn_123';
        final testResponseData = {
          'transaction_id': testTransactionId,
          'payment_id': 'pay_123',
          'session_id': 'session_123',
          'invoice_number': 'INV001',
          'amount': 1500.0,
          'currency': 'INR',
          'status': 'success',
          'timestamp': DateTime.now().toIso8601String(),
        };

        final mockResponse = MockResponse();
        when(() => mockResponse.statusCode).thenReturn(200);
        when(() => mockResponse.data).thenReturn(jsonEncode(testResponseData));
        when(
          () => mockApiClient.get(
            '${AppConstants.baseUrl}/zoho/payments/status/$testTransactionId',
          ),
        ).thenAnswer((_) async => mockResponse);

        // Act
        final result = await dataSource.getPaymentStatus(testTransactionId);

        // Assert
        expect(result, isA<PaymentTransactionModel>());
        expect(result.transactionId, equals(testTransactionId));
        expect(result.status, equals('success'));

        verify(
          () => mockApiClient.get(
            '${AppConstants.baseUrl}/zoho/payments/status/$testTransactionId',
          ),
        ).called(1);
      });

      test('should handle API error when getting payment status', () async {
        // Arrange
        final testTransactionId = 'txn_123';
        final mockResponse = MockResponse();
        when(() => mockResponse.statusCode).thenReturn(404);
        when(
          () => mockApiClient.get(any()),
        ).thenAnswer((_) async => mockResponse);

        // Act & Assert
        expect(
          () => dataSource.getPaymentStatus(testTransactionId),
          throwsA(isA<ServerException>()),
        );
      });

      test('should handle network error when getting payment status', () async {
        // Arrange
        final testTransactionId = 'txn_123';
        when(() => mockApiClient.get(any())).thenThrow(
          DioException(
            requestOptions: RequestOptions(path: '/test'),
            type: DioExceptionType.connectionTimeout,
          ),
        );

        // Act & Assert
        expect(
          () => dataSource.getPaymentStatus(testTransactionId),
          throwsA(isA<ServerException>()),
        );
      });
    });
  });
}
