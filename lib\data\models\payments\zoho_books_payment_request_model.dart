import '../../../domain/entities/payments/zoho_books_payment_request.dart';

/// Data model for Zoho Books payment request
class ZohoBooksPaymentRequestModel {
  final String invoiceId;
  final double amount;
  final String currency;
  final String description;

  ZohoBooksPaymentRequestModel({
    required this.invoiceId,
    required this.amount,
    required this.currency,
    required this.description,
  });

  /// Creates ZohoBooksPaymentRequestModel from domain entity
  factory ZohoBooksPaymentRequestModel.fromEntity(ZohoBooksPaymentRequest entity) {
    return ZohoBooksPaymentRequestModel(
      invoiceId: entity.invoiceId,
      amount: entity.amount,
      currency: entity.currency,
      description: entity.description,
    );
  }

  /// Converts to JSON for API request
  Map<String, dynamic> toJson() {
    return {
      'invoice_id': invoiceId,
      'amount': amount,
      'currency': currency,
      'description': description,
    };
  }

  /// Converts to domain entity
  ZohoBooksPaymentRequest toEntity() {
    return ZohoBooksPaymentRequest(
      invoiceId: invoiceId,
      amount: amount,
      currency: currency,
      description: description,
    );
  }
}
