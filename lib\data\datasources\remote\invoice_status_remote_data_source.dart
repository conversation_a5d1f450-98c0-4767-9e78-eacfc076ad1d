import 'dart:convert';
import 'package:dio/dio.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/error/exceptions.dart';
import '../../../core/network/api_client.dart';
import '../../../core/utils/logger.dart';
import '../../models/invoices/invoice_status_update_request_model.dart';
import '../../models/invoices/invoice_status_update_response_model.dart';

/// Abstract interface for invoice status remote data source
abstract class InvoiceStatusRemoteDataSource {
  /// Updates the status of an invoice after successful payment
  Future<InvoiceStatusUpdateResponseModel> updateInvoiceStatus(
    InvoiceStatusUpdateRequestModel request,
  );
}

/// Implementation of invoice status remote data source
class InvoiceStatusRemoteDataSourceImpl implements InvoiceStatusRemoteDataSource {
  final ApiClient apiClient;

  InvoiceStatusRemoteDataSourceImpl({
    required this.apiClient,
  });

  @override
  Future<InvoiceStatusUpdateResponseModel> updateInvoiceStatus(
    InvoiceStatusUpdateRequestModel request,
  ) async {
    try {
      logger.i('Updating invoice status for invoice: ${request.invoiceId}');

      // Prepare the request payload
      final requestPayload = request.toJson();

      logger.d('Invoice status update request payload: $requestPayload');
      logger.d('Invoice status update details:');
      logger.d('  - Invoice ID: ${request.invoiceId}');
      logger.d('  - Payment Method: ${request.paymentDetails.paymentMethod}');
      logger.d('  - Payment Amount: ${request.paymentDetails.paymentAmount}');
      logger.d('  - Transaction ID: ${request.paymentDetails.transactionId}');
      logger.d('  - Payment Date: ${request.paymentDetails.paymentDate}');

      // Call the invoice status update endpoint
      final response = await apiClient.post(
        '${AppConstants.baseUrl}/invoices/update-status',
        data: requestPayload,
      );

      // Accept both 200 (OK) and 201 (Created) as success
      if (response.statusCode == 200 || response.statusCode == 201) {
        try {
          final jsonData = jsonDecode(response.data) as Map<String, dynamic>;
          logger.i(
            'Invoice status updated successfully with status ${response.statusCode}',
          );
          logger.d('Response data: $jsonData');

          // Validate that the response contains the required fields
          if (!jsonData.containsKey('success')) {
            logger.e('API response missing success field');
            logger.e('Available fields: ${jsonData.keys.toList()}');
            logger.e('Full response: $jsonData');
            throw ServerException();
          }

          final success = jsonData['success'] ?? false;
          if (!success) {
            logger.e('API returned success: false');
            logger.e('Response message: ${jsonData['message']}');
            logger.e('Full response: $jsonData');
            throw ServerException();
          }

          // Validate that the response contains the data field
          if (!jsonData.containsKey('data') || jsonData['data'] == null) {
            logger.e('API response missing data field');
            logger.e('Available fields: ${jsonData.keys.toList()}');
            logger.e('Full response: $jsonData');
            throw ServerException();
          }

          logger.i('Successfully updated invoice status');
          logger.d('Previous status: ${jsonData['data']['previous_status']}');
          logger.d('Current status: ${jsonData['data']['current_status']}');

          return InvoiceStatusUpdateResponseModel.fromJson(jsonData);
        } catch (e) {
          logger.e('Error parsing successful API response: ${e.toString()}');
          logger.e('Raw response data: ${response.data}');
          logger.e('Response type: ${response.data.runtimeType}');
          throw ServerException();
        }
      } else {
        // Enhanced error logging for debugging
        logger.e('Failed to update invoice status:');
        logger.e('  - Status Code: ${response.statusCode}');
        logger.e('  - Response Headers: ${response.headers}');
        logger.e('  - Response Data: ${response.data}');
        logger.e(
          '  - Request URL: ${AppConstants.baseUrl}/invoices/update-status',
        );
        logger.e('  - Request Payload: $requestPayload');

        // Try to parse error response if it's JSON
        try {
          if (response.data != null) {
            final errorData = jsonDecode(response.data) as Map<String, dynamic>;
            logger.e('  - Parsed Error: $errorData');
            if (errorData.containsKey('error')) {
              logger.e('  - Error Message: ${errorData['error']}');
            }
            if (errorData.containsKey('message')) {
              logger.e('  - API Message: ${errorData['message']}');
            }
          }
        } catch (parseError) {
          logger.e('  - Could not parse error response: $parseError');
        }

        throw ServerException();
      }
    } catch (e) {
      logger.e('Error updating invoice status: ${e.toString()}');
      logger.e('Error type: ${e.runtimeType}');

      // Enhanced error logging for different exception types
      if (e is DioException) {
        logger.e('DioException details:');
        logger.e('  - Type: ${e.type}');
        logger.e('  - Message: ${e.message}');
        logger.e('  - Response: ${e.response?.data}');
        logger.e('  - Status Code: ${e.response?.statusCode}');
        logger.e('  - Request URL: ${e.requestOptions.path}');
        logger.e('  - Request Data: ${e.requestOptions.data}');
      }

      if (e is ServerException) {
        rethrow;
      }
      throw ServerException();
    }
  }
}
