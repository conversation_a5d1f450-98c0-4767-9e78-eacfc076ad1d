import 'package:equatable/equatable.dart';
import '../../../domain/entities/payments/payment_session.dart';
import '../../../domain/entities/payments/payment_transaction.dart';

/// Base state for payment operations
abstract class PaymentState extends Equatable {
  const PaymentState();

  @override
  List<Object?> get props => [];
}

/// Initial state when payment cubit is created
class PaymentInitial extends PaymentState {}

/// State when creating payment session
class PaymentSessionLoading extends PaymentState {}

/// State when payment session is created successfully
class PaymentSessionCreated extends PaymentState {
  final PaymentSession session;

  const PaymentSessionCreated(this.session);

  @override
  List<Object?> get props => [session];
}

/// State when payment is being processed
class PaymentProcessing extends PaymentState {
  final String sessionId;
  final String invoiceNumber;

  const PaymentProcessing({
    required this.sessionId,
    required this.invoiceNumber,
  });

  @override
  List<Object?> get props => [sessionId, invoiceNumber];
}

/// State when payment is completed successfully
class PaymentSuccess extends PaymentState {
  final PaymentTransaction transaction;

  const PaymentSuccess(this.transaction);

  @override
  List<Object?> get props => [transaction];
}

/// State when payment fails
class PaymentFailure extends PaymentState {
  final String message;
  final String? invoiceNumber;

  const PaymentFailure({
    required this.message,
    this.invoiceNumber,
  });

  @override
  List<Object?> get props => [message, invoiceNumber];
}

/// State when payment is cancelled by user
class PaymentCancelled extends PaymentState {
  final String sessionId;
  final String invoiceNumber;

  const PaymentCancelled({
    required this.sessionId,
    required this.invoiceNumber,
  });

  @override
  List<Object?> get props => [sessionId, invoiceNumber];
}

/// State when checking payment status
class PaymentStatusLoading extends PaymentState {
  final String transactionId;

  const PaymentStatusLoading(this.transactionId);

  @override
  List<Object?> get props => [transactionId];
}

/// State when payment status is retrieved
class PaymentStatusLoaded extends PaymentState {
  final PaymentTransaction transaction;

  const PaymentStatusLoaded(this.transaction);

  @override
  List<Object?> get props => [transaction];
}
