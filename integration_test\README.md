# AquaPartner Authentication Flow Integration Tests

This directory contains comprehensive integration tests for the AquaPartner authentication flow, covering phone number authentication with OTP verification.

## Test Overview

The integration tests validate the complete authentication user journey from phone number entry to successful authentication, including both success and failure scenarios.

### Test Constants

- **Test Phone Number**: `9999999999` (used for all authentication scenarios)
- **Valid Test OTP**: `123456` (for successful authentication)
- **Invalid Test OTP**: `123123` (for failed authentication scenarios)

## Test Cases

### 1. Complete Authentication Flow - Valid OTP Success Path

**File**: `comprehensive_auth_flow_test.dart`
**Test**: `Complete Authentication Flow - Valid OTP Success Path`

**Description**: Tests the complete successful authentication flow using valid OTP.

**Expected Flow**:
```
AuthInitial → AuthLoading → OtpSent → AuthLoading → AuthSuccess
```

**Test Steps**:
1. Enter phone number (9999999999) and send OTP
2. Verify state transitions to OtpSent with correct verification ID
3. Navigate to OTP verification screen
4. Enter valid OTP (123456) and verify
5. Verify state reaches AuthSuccess with authenticated user
6. <PERSON><PERSON> expected navigation errors in test environment

**Assertions**:
- ✅ Phone number entry and OTP sending works correctly
- ✅ State transitions properly through all expected states
- ✅ OTP verification with valid OTP succeeds
- ✅ User is properly authenticated and verified
- ✅ All use cases called with correct parameters

### 2. Complete Authentication Flow - Invalid OTP Error Path

**File**: `comprehensive_auth_flow_test.dart`
**Test**: `Complete Authentication Flow - Invalid OTP Error Path`

**Description**: Tests the authentication flow with invalid OTP to verify proper error handling.

**Expected Flow**:
```
AuthInitial → AuthLoading → OtpSent → AuthLoading → OtpVerificationError
```

**Test Steps**:
1. Enter phone number (9999999999) and send OTP
2. Verify state transitions to OtpSent
3. Navigate to OTP verification screen
4. Enter invalid OTP (123123) and verify
5. Verify state reaches OtpVerificationError with proper error message
6. Verify customer lookup and user saving are NOT called

**Assertions**:
- ✅ OTP verification with invalid OTP fails as expected
- ✅ State transitions to OtpVerificationError
- ✅ Error message is properly displayed
- ✅ Customer lookup and user saving not called for failed authentication
- ✅ Proper error handling for invalid OTP scenarios

### 3. Complete Authentication Flow - State Progression Validation

**File**: `comprehensive_auth_flow_test.dart`
**Test**: `Complete Authentication Flow - State Progression Validation`

**Description**: Validates the complete state progression and ensures proper state transitions at each step.

**Test Steps**:
1. Track all state changes throughout the authentication flow
2. Verify AuthLoading states appear during async operations
3. Validate complete state progression order
4. Ensure all intermediate states are properly handled

**Assertions**:
- ✅ Complete state progression validated
- ✅ AuthLoading states appear during async operations
- ✅ OtpSent state contains correct verification data
- ✅ AuthSuccess state reached after successful authentication
- ✅ State transitions occur in correct order

## Test Architecture

### Mock Classes

The tests use comprehensive mocking to isolate the authentication logic:

- `MockSendOtpUseCase`: Mocks OTP sending functionality
- `MockVerifyOtpUseCase`: Mocks OTP verification with configurable responses
- `MockGetCustomerByMobileNumber`: Mocks customer lookup
- `MockSaveUserUseCase`: Mocks user persistence
- `MockNavigationService`: Handles navigation in test environment
- `MockAnalyticsService`: Mocks analytics tracking
- `MockConnectivity`: Mocks network connectivity

### Test Helpers

- `setupSuccessfulAuthMocks()`: Configures mocks for successful authentication
- `createAuthCubit()`: Creates AuthCubit with mocked dependencies
- `createTestWidget()`: Creates test widget with proper BLoC providers

## Running the Tests

### Command Line

```bash
# Run all integration tests
flutter test integration_test/

# Run specific authentication tests
flutter test integration_test/comprehensive_auth_flow_test.dart

# Run with verbose output
flutter test integration_test/comprehensive_auth_flow_test.dart --verbose
```

### Using the Test Script

```bash
# Windows
scripts\run_auth_integration_tests.bat

# The script will:
# 1. Check Flutter installation
# 2. Get dependencies
# 3. Run the integration tests
# 4. Provide detailed test results
```

## Expected Behavior

### For Valid OTP (123456)
- Authentication should succeed and reach `AuthSuccess` state
- User should be properly authenticated and verified
- All authentication use cases should be called correctly
- Navigation errors in test environment are expected and handled

### For Invalid OTP (123123)
- Authentication should fail with `OtpVerificationError` state
- Appropriate error message should be displayed
- Customer lookup and user saving should NOT be called
- Error handling should be robust and user-friendly

## Test Environment Considerations

### Navigation Errors
The tests expect navigation errors to occur in the test environment due to `MockNavigationService` limitations. This is NOT an application bug but a limitation of the test setup where the mock navigator key is not attached to a real Navigator widget.

**Expected Error**: `Null check operator used on a null value`
**Cause**: `AppRouter.navigateToHome()` tries to access `_navigationService.navigatorKey.currentState!` which is null in tests
**Handling**: Tests catch and handle this error, considering the test passing when `AuthSuccess` state is reached

### State Timing
Tests include appropriate delays (`Duration(milliseconds: 100-200)`) to allow for state transitions and async operations to complete properly.

## Test Coverage

The integration tests provide comprehensive coverage of:

1. **Complete User Journey**: From phone entry to authentication completion
2. **State Management**: All authentication state transitions
3. **Error Handling**: Invalid OTP and failure scenarios
4. **Use Case Integration**: Proper calling of all authentication use cases
5. **UI Integration**: Widget interactions and screen navigation
6. **Edge Cases**: Network errors, validation failures, and timeout scenarios

## Troubleshooting

### Common Issues

1. **Widget Not Found**: Ensure proper widget keys and selectors
2. **State Timing**: Adjust pump delays if state transitions are too fast/slow
3. **Mock Setup**: Verify all required mocks are properly configured
4. **Dependency Injection**: Ensure GetIt service locator is properly reset between tests

### Debug Tips

- Use `print` statements to track state changes and test progress
- Verify mock call counts with `verify()` and `verifyNever()`
- Check state history for unexpected state transitions
- Ensure proper test cleanup between test cases

## Integration with CI/CD

These tests are designed to run in automated environments and provide clear pass/fail results with detailed error messages for debugging failed scenarios.
