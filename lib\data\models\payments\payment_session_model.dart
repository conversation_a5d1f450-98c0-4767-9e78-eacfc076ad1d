import '../../../domain/entities/payments/payment_session.dart';

/// Data model for payment session
class PaymentSessionModel {
  final String sessionId;
  final String sessionUrl;
  final String paymentSessionId;
  final String currency;
  final double amount;
  final int createdTime;
  final List<PaymentMetaDataModel> metaData;

  PaymentSessionModel({
    required this.sessionId,
    required this.sessionUrl,
    required this.paymentSessionId,
    required this.currency,
    required this.amount,
    required this.createdTime,
    this.metaData = const [],
  });

  /// Creates PaymentSessionModel from JSON response
  factory PaymentSessionModel.fromJson(Map<String, dynamic> json) {
    final data = json['data'] ?? json;
    final paymentSession = json['payment_session'] ?? {};

    return PaymentSessionModel(
      sessionId:
          data['payment_session_id']?.toString() ??
          paymentSession['payments_session_id']?.toString() ??
          '',
      sessionUrl:
          'https://payments.zoho.in/checkout/${data['payment_session_id'] ?? paymentSession['payments_session_id']}',
      paymentSessionId:
          data['payment_session_id']?.toString() ??
          paymentSession['payments_session_id']?.toString() ??
          '',
      currency:
          data['currency']?.toString() ??
          paymentSession['currency']?.toString() ??
          'INR',
      amount: _parseAmount(data['amount'] ?? paymentSession['amount']),
      createdTime: _parseCreatedTime(
        data['created_time'] ?? paymentSession['created_time'],
      ),
      metaData:
          (paymentSession['meta_data'] as List<dynamic>?)
              ?.map((item) => PaymentMetaDataModel.fromJson(item))
              .toList() ??
          [],
    );
  }

  /// Creates PaymentSessionModel from Zoho payment link API response
  ///
  /// Expected response structure:
  /// {
  ///   "success": true,
  ///   "message": "Payment link created successfully",
  ///   "data": {
  ///     "payment_link_id": "5619000000248086",
  ///     "amount": "101.50",
  ///     "currency": "INR",
  ///     "description": "Payment for Order #12345",
  ///     "status": "active",
  ///     "created_time": 1751043827,
  ///     "expires_at": "2025-07-27",
  ///     "transaction_id": "685ecef3e19640cfcac11ebf"
  ///   },
  ///   "payment_link": {
  ///     "url": "https://payments.zoho.in/paymentlinks/[payment_token]",
  ///     "expires_at": "2025-07-27",
  ///     "amount": "101.50",
  ///     "currency": "INR",
  ///     "status": "active"
  ///   }
  /// }
  factory PaymentSessionModel.fromZohoLinkResponse(Map<String, dynamic> json) {
    final data = json['data'] ?? {};
    final paymentLink = json['payment_link'] ?? {};

    // Extract payment link ID to use as session ID
    final paymentLinkId =
        data['payment_link_id']?.toString() ??
        data['transaction_id']?.toString() ??
        DateTime.now().millisecondsSinceEpoch.toString();

    // Extract payment URL from payment_link section
    final paymentUrl = paymentLink['url']?.toString() ?? '';

    return PaymentSessionModel(
      sessionId: paymentLinkId,
      sessionUrl: paymentUrl,
      paymentSessionId: paymentLinkId,
      currency:
          data['currency']?.toString() ??
          paymentLink['currency']?.toString() ??
          'INR',
      amount: _parseAmount(data['amount'] ?? paymentLink['amount']),
      createdTime: _parseCreatedTime(data['created_time']),
      metaData: [], // Meta data not included in this response format
    );
  }

  /// Safely parses amount from various formats (string, int, double)
  static double _parseAmount(dynamic amount) {
    if (amount == null) return 0.0;

    if (amount is double) return amount;
    if (amount is int) return amount.toDouble();
    if (amount is String) {
      try {
        return double.parse(amount);
      } catch (e) {
        // If parsing fails, try to extract numeric value
        final numericString = amount.replaceAll(RegExp(r'[^0-9.]'), '');
        return double.tryParse(numericString) ?? 0.0;
      }
    }

    // Fallback for any other type
    return 0.0;
  }

  /// Safely parses created time from various formats
  static int _parseCreatedTime(dynamic createdTime) {
    if (createdTime == null) return 0;

    if (createdTime is int) return createdTime;
    if (createdTime is String) {
      return int.tryParse(createdTime) ?? 0;
    }
    if (createdTime is double) return createdTime.toInt();

    return 0;
  }

  /// Converts to domain entity
  PaymentSession toEntity() {
    return PaymentSession(
      sessionId: sessionId,
      sessionUrl: sessionUrl,
      paymentSessionId: paymentSessionId,
      currency: currency,
      amount: amount,
      createdTime: createdTime,
      metaData: metaData.map((item) => item.toEntity()).toList(),
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'session_id': sessionId,
      'session_url': sessionUrl,
      'payment_session_id': paymentSessionId,
      'currency': currency,
      'amount': amount,
      'created_time': createdTime,
      'meta_data': metaData.map((item) => item.toJson()).toList(),
    };
  }
}

/// Data model for payment metadata
class PaymentMetaDataModel {
  final String key;
  final String value;

  PaymentMetaDataModel({required this.key, required this.value});

  /// Creates PaymentMetaDataModel from JSON
  factory PaymentMetaDataModel.fromJson(Map<String, dynamic> json) {
    return PaymentMetaDataModel(
      key: json['key']?.toString() ?? '',
      value: json['value']?.toString() ?? '',
    );
  }

  /// Converts to domain entity
  PaymentMetaData toEntity() {
    return PaymentMetaData(key: key, value: value);
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {'key': key, 'value': value};
  }
}
