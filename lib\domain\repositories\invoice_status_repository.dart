import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/invoices/invoice_status_update_request.dart';
import '../entities/invoices/invoice_status_update_response.dart';

/// Repository interface for invoice status operations
abstract class InvoiceStatusRepository {
  /// Updates the status of an invoice after successful payment
  /// 
  /// Returns [InvoiceStatusUpdateResponse] on success
  /// Returns [Failure] on error (NetworkFailure, ServerFailure, etc.)
  Future<Either<Failure, InvoiceStatusUpdateResponse>> updateInvoiceStatus(
    InvoiceStatusUpdateRequest request,
  );
}
