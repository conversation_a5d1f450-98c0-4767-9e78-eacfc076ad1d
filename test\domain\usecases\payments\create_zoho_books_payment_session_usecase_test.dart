import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/domain/entities/payments/zoho_books_payment_request.dart';
import 'package:aquapartner/domain/entities/payments/zoho_books_payment_session.dart';
import 'package:aquapartner/domain/repositories/zoho_books_payment_repository.dart';
import 'package:aquapartner/domain/usecases/payments/create_zoho_books_payment_session_usecase.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockZohoBooksPaymentRepository extends Mock implements ZohoBooksPaymentRepository {}

void main() {
  group('CreateZohoBooksPaymentSessionUseCase', () {
    late CreateZohoBooksPaymentSessionUseCase useCase;
    late MockZohoBooksPaymentRepository mockRepository;

    setUp(() {
      mockRepository = MockZohoBooksPaymentRepository();
      useCase = CreateZohoBooksPaymentSessionUseCase(mockRepository);
    });

    group('call', () {
      late ZohoBooksPaymentRequest testRequest;
      late ZohoBooksPaymentSession testSession;

      setUp(() {
        testRequest = ZohoBooksPaymentRequest(
          invoiceId: 'INV001',
          amount: 1500.0,
          currency: 'INR',
          description: 'Payment for AquaPartner Invoice INV001',
        );

        testSession = ZohoBooksPaymentSession(
          invoiceId: 'INV001',
          paymentLinkUrl: 'https://books.zoho.in/checkout/test_token',
          amount: 1500.0,
          currency: 'INR',
          description: 'Payment for AquaPartner Invoice INV001',
          createdAt: DateTime.now(),
        );

        // Register fallback value for ZohoBooksPaymentRequest
        registerFallbackValue(ZohoBooksPaymentRequest(
          invoiceId: 'fallback_invoice',
          amount: 100.0,
          currency: 'INR',
          description: 'Fallback description',
        ));
      });

      test('should return ZohoBooksPaymentSession when repository call succeeds', () async {
        // Arrange
        when(() => mockRepository.createPaymentLink(any()))
            .thenAnswer((_) async => Right(testSession));

        // Act
        final result = await useCase(testRequest);

        // Assert
        expect(result, isA<Right<Failure, ZohoBooksPaymentSession>>());
        result.fold(
          (failure) => fail('Expected Right but got Left'),
          (session) {
            expect(session, equals(testSession));
          },
        );
        verify(() => mockRepository.createPaymentLink(testRequest)).called(1);
      });

      test('should return ServerFailure when repository call fails', () async {
        // Arrange
        when(() => mockRepository.createPaymentLink(any()))
            .thenAnswer((_) async => Left(ServerFailure()));

        // Act
        final result = await useCase(testRequest);

        // Assert
        expect(result, isA<Left<Failure, ZohoBooksPaymentSession>>());
        result.fold(
          (failure) => expect(failure, isA<ServerFailure>()),
          (_) => fail('Expected Left but got Right'),
        );
        verify(() => mockRepository.createPaymentLink(testRequest)).called(1);
      });

      group('validation', () {
        test('should return ValidationFailure when amount is zero', () async {
          // Arrange
          final invalidRequest = ZohoBooksPaymentRequest(
            invoiceId: 'INV001',
            amount: 0.0,
            currency: 'INR',
            description: 'Payment for AquaPartner Invoice INV001',
          );

          // Act
          final result = await useCase(invalidRequest);

          // Assert
          expect(result, isA<Left<Failure, ZohoBooksPaymentSession>>());
          result.fold(
            (failure) {
              expect(failure, isA<ValidationFailure>());
              expect((failure as ValidationFailure).message, 'Amount must be greater than zero');
            },
            (_) => fail('Expected Left but got Right'),
          );
          verifyNever(() => mockRepository.createPaymentLink(any()));
        });

        test('should return ValidationFailure when amount is negative', () async {
          // Arrange
          final invalidRequest = ZohoBooksPaymentRequest(
            invoiceId: 'INV001',
            amount: -100.0,
            currency: 'INR',
            description: 'Payment for AquaPartner Invoice INV001',
          );

          // Act
          final result = await useCase(invalidRequest);

          // Assert
          expect(result, isA<Left<Failure, ZohoBooksPaymentSession>>());
          result.fold(
            (failure) {
              expect(failure, isA<ValidationFailure>());
              expect((failure as ValidationFailure).message, 'Amount must be greater than zero');
            },
            (_) => fail('Expected Left but got Right'),
          );
          verifyNever(() => mockRepository.createPaymentLink(any()));
        });

        test('should return ValidationFailure when invoiceId is empty', () async {
          // Arrange
          final invalidRequest = ZohoBooksPaymentRequest(
            invoiceId: '',
            amount: 1500.0,
            currency: 'INR',
            description: 'Payment for AquaPartner Invoice',
          );

          // Act
          final result = await useCase(invalidRequest);

          // Assert
          expect(result, isA<Left<Failure, ZohoBooksPaymentSession>>());
          result.fold(
            (failure) {
              expect(failure, isA<ValidationFailure>());
              expect((failure as ValidationFailure).message, 'Invoice ID is required');
            },
            (_) => fail('Expected Left but got Right'),
          );
          verifyNever(() => mockRepository.createPaymentLink(any()));
        });

        test('should return ValidationFailure when description is empty', () async {
          // Arrange
          final invalidRequest = ZohoBooksPaymentRequest(
            invoiceId: 'INV001',
            amount: 1500.0,
            currency: 'INR',
            description: '',
          );

          // Act
          final result = await useCase(invalidRequest);

          // Assert
          expect(result, isA<Left<Failure, ZohoBooksPaymentSession>>());
          result.fold(
            (failure) {
              expect(failure, isA<ValidationFailure>());
              expect((failure as ValidationFailure).message, 'Description is required');
            },
            (_) => fail('Expected Left but got Right'),
          );
          verifyNever(() => mockRepository.createPaymentLink(any()));
        });

        test('should return ValidationFailure when currency is empty', () async {
          // Arrange
          final invalidRequest = ZohoBooksPaymentRequest(
            invoiceId: 'INV001',
            amount: 1500.0,
            currency: '',
            description: 'Payment for AquaPartner Invoice INV001',
          );

          // Act
          final result = await useCase(invalidRequest);

          // Assert
          expect(result, isA<Left<Failure, ZohoBooksPaymentSession>>());
          result.fold(
            (failure) {
              expect(failure, isA<ValidationFailure>());
              expect((failure as ValidationFailure).message, 'Currency is required');
            },
            (_) => fail('Expected Left but got Right'),
          );
          verifyNever(() => mockRepository.createPaymentLink(any()));
        });
      });
    });
  });
}
