import '../../../domain/entities/invoices/invoice_status_update_response.dart';

/// Data model for invoice status update data from API response
class InvoiceStatusUpdateDataModel {
  final String invoiceId;
  final String previousStatus;
  final String currentStatus;
  final String paymentCompletedAt;
  final String updatedAt;

  InvoiceStatusUpdateDataModel({
    required this.invoiceId,
    required this.previousStatus,
    required this.currentStatus,
    required this.paymentCompletedAt,
    required this.updatedAt,
  });

  /// Creates InvoiceStatusUpdateDataModel from JSON response
  factory InvoiceStatusUpdateDataModel.fromJson(Map<String, dynamic> json) {
    return InvoiceStatusUpdateDataModel(
      invoiceId: json['invoiceId']?.toString() ?? '',
      previousStatus: json['previous_status']?.toString() ?? '',
      currentStatus: json['current_status']?.toString() ?? '',
      paymentCompletedAt: json['payment_completed_at']?.toString() ?? DateTime.now().toIso8601String(),
      updatedAt: json['updated_at']?.toString() ?? DateTime.now().toIso8601String(),
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'invoiceId': invoiceId,
      'previous_status': previousStatus,
      'current_status': currentStatus,
      'payment_completed_at': paymentCompletedAt,
      'updated_at': updatedAt,
    };
  }

  /// Converts to domain entity
  InvoiceStatusUpdateData toEntity() {
    return InvoiceStatusUpdateData(
      invoiceId: invoiceId,
      previousStatus: previousStatus,
      currentStatus: currentStatus,
      paymentCompletedAt: DateTime.parse(paymentCompletedAt),
      updatedAt: DateTime.parse(updatedAt),
    );
  }
}

/// Data model for invoice status update response from API
class InvoiceStatusUpdateResponseModel {
  final bool success;
  final String message;
  final InvoiceStatusUpdateDataModel data;

  InvoiceStatusUpdateResponseModel({
    required this.success,
    required this.message,
    required this.data,
  });

  /// Creates InvoiceStatusUpdateResponseModel from JSON response
  factory InvoiceStatusUpdateResponseModel.fromJson(Map<String, dynamic> json) {
    return InvoiceStatusUpdateResponseModel(
      success: json['success'] ?? false,
      message: json['message']?.toString() ?? '',
      data: InvoiceStatusUpdateDataModel.fromJson(json['data'] ?? {}),
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data.toJson(),
    };
  }

  /// Converts to domain entity
  InvoiceStatusUpdateResponse toEntity() {
    return InvoiceStatusUpdateResponse(
      success: success,
      message: message,
      data: data.toEntity(),
    );
  }
}
