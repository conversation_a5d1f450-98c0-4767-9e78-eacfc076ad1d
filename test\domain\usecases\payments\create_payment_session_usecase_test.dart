import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/domain/entities/payments/payment_request.dart';
import 'package:aquapartner/domain/entities/payments/payment_session.dart';
import 'package:aquapartner/domain/repositories/payment_repository.dart';
import 'package:aquapartner/domain/usecases/payments/create_payment_session_usecase.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockPaymentRepository extends Mock implements PaymentRepository {}

void main() {
  group('CreatePaymentSessionUseCase', () {
    late CreatePaymentSessionUseCase useCase;
    late MockPaymentRepository mockRepository;

    setUp(() {
      mockRepository = MockPaymentRepository();
      useCase = CreatePaymentSessionUseCase(mockRepository);
    });

    group('call', () {
      late PaymentRequest testRequest;
      late PaymentSession testSession;

      setUp(() {
        testRequest = PaymentRequest(
          amount: 1500.0,
          currencyCode: 'INR',
          customerId: 'customer_123',
          invoiceNumber: 'INV001',
          description: 'Payment for AquaPartner Invoice INV001',
          customerName: 'Test Customer',
          customerEmail: '<EMAIL>',
          customerPhone: '+919999999999',
        );

        testSession = PaymentSession(
          sessionId: 'session_123',
          sessionUrl: 'https://payments.zoho.in/paymentlinks/test_token',
          paymentSessionId: 'session_123',
          currency: 'INR',
          amount: 1500.0,
          createdTime: DateTime.now().millisecondsSinceEpoch,
        );
      });

      test('should return PaymentSession when repository call succeeds', () async {
        // Arrange
        when(() => mockRepository.createPaymentSession(testRequest))
            .thenAnswer((_) async => Right(testSession));

        // Act
        final result = await useCase(testRequest);

        // Assert
        expect(result, isA<Right<Failure, PaymentSession>>());
        result.fold(
          (failure) => fail('Expected Right but got Left'),
          (session) {
            expect(session.sessionId, equals('session_123'));
            expect(session.amount, equals(1500.0));
          },
        );

        verify(() => mockRepository.createPaymentSession(testRequest)).called(1);
      });

      test('should return Failure when repository call fails', () async {
        // Arrange
        when(() => mockRepository.createPaymentSession(testRequest))
            .thenAnswer((_) async => Left(ServerFailure()));

        // Act
        final result = await useCase(testRequest);

        // Assert
        expect(result, isA<Left<Failure, PaymentSession>>());
        result.fold(
          (failure) => expect(failure, isA<ServerFailure>()),
          (session) => fail('Expected Left but got Right'),
        );

        verify(() => mockRepository.createPaymentSession(testRequest)).called(1);
      });

      group('validation', () {
        test('should return ValidationFailure when amount is zero', () async {
          // Arrange
          final invalidRequest = PaymentRequest(
            amount: 0.0,
            currencyCode: 'INR',
            customerId: 'customer_123',
            invoiceNumber: 'INV001',
            description: 'Test payment',
          );

          // Act
          final result = await useCase(invalidRequest);

          // Assert
          expect(result, isA<Left<Failure, PaymentSession>>());
          result.fold(
            (failure) {
              expect(failure, isA<ValidationFailure>());
              expect((failure as ValidationFailure).message, 
                     equals('Amount must be greater than zero'));
            },
            (session) => fail('Expected Left but got Right'),
          );

          verifyNever(() => mockRepository.createPaymentSession(any()));
        });

        test('should return ValidationFailure when amount is negative', () async {
          // Arrange
          final invalidRequest = PaymentRequest(
            amount: -100.0,
            currencyCode: 'INR',
            customerId: 'customer_123',
            invoiceNumber: 'INV001',
            description: 'Test payment',
          );

          // Act
          final result = await useCase(invalidRequest);

          // Assert
          expect(result, isA<Left<Failure, PaymentSession>>());
          result.fold(
            (failure) {
              expect(failure, isA<ValidationFailure>());
              expect((failure as ValidationFailure).message, 
                     equals('Amount must be greater than zero'));
            },
            (session) => fail('Expected Left but got Right'),
          );

          verifyNever(() => mockRepository.createPaymentSession(any()));
        });

        test('should return ValidationFailure when customerId is empty', () async {
          // Arrange
          final invalidRequest = PaymentRequest(
            amount: 1500.0,
            currencyCode: 'INR',
            customerId: '',
            invoiceNumber: 'INV001',
            description: 'Test payment',
          );

          // Act
          final result = await useCase(invalidRequest);

          // Assert
          expect(result, isA<Left<Failure, PaymentSession>>());
          result.fold(
            (failure) {
              expect(failure, isA<ValidationFailure>());
              expect((failure as ValidationFailure).message, 
                     equals('Customer ID is required'));
            },
            (session) => fail('Expected Left but got Right'),
          );

          verifyNever(() => mockRepository.createPaymentSession(any()));
        });

        test('should return ValidationFailure when invoiceNumber is empty', () async {
          // Arrange
          final invalidRequest = PaymentRequest(
            amount: 1500.0,
            currencyCode: 'INR',
            customerId: 'customer_123',
            invoiceNumber: '',
            description: 'Test payment',
          );

          // Act
          final result = await useCase(invalidRequest);

          // Assert
          expect(result, isA<Left<Failure, PaymentSession>>());
          result.fold(
            (failure) {
              expect(failure, isA<ValidationFailure>());
              expect((failure as ValidationFailure).message, 
                     equals('Invoice number is required'));
            },
            (session) => fail('Expected Left but got Right'),
          );

          verifyNever(() => mockRepository.createPaymentSession(any()));
        });

        test('should return ValidationFailure when description is empty', () async {
          // Arrange
          final invalidRequest = PaymentRequest(
            amount: 1500.0,
            currencyCode: 'INR',
            customerId: 'customer_123',
            invoiceNumber: 'INV001',
            description: '',
          );

          // Act
          final result = await useCase(invalidRequest);

          // Assert
          expect(result, isA<Left<Failure, PaymentSession>>());
          result.fold(
            (failure) {
              expect(failure, isA<ValidationFailure>());
              expect((failure as ValidationFailure).message, 
                     equals('Description is required'));
            },
            (session) => fail('Expected Left but got Right'),
          );

          verifyNever(() => mockRepository.createPaymentSession(any()));
        });

        test('should return ValidationFailure when currencyCode is empty', () async {
          // Arrange
          final invalidRequest = PaymentRequest(
            amount: 1500.0,
            currencyCode: '',
            customerId: 'customer_123',
            invoiceNumber: 'INV001',
            description: 'Test payment',
          );

          // Act
          final result = await useCase(invalidRequest);

          // Assert
          expect(result, isA<Left<Failure, PaymentSession>>());
          result.fold(
            (failure) {
              expect(failure, isA<ValidationFailure>());
              expect((failure as ValidationFailure).message, 
                     equals('Currency code is required'));
            },
            (session) => fail('Expected Left but got Right'),
          );

          verifyNever(() => mockRepository.createPaymentSession(any()));
        });

        test('should proceed when all required fields are valid', () async {
          // Arrange
          when(() => mockRepository.createPaymentSession(testRequest))
              .thenAnswer((_) async => Right(testSession));

          // Act
          final result = await useCase(testRequest);

          // Assert
          expect(result, isA<Right<Failure, PaymentSession>>());
          verify(() => mockRepository.createPaymentSession(testRequest)).called(1);
        });
      });
    });
  });
}
