import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/payments/zoho_books_payment_request.dart';
import '../entities/payments/zoho_books_payment_session.dart';

/// Repository interface for Zoho Books payment operations
abstract class ZohoBooksPaymentRepository {
  /// Creates a payment link with Zoho Books payment gateway
  /// 
  /// Returns [ZohoBooksPaymentSession] on success or [Failure] on error
  Future<Either<Failure, ZohoBooksPaymentSession>> createPaymentLink(
    ZohoBooksPaymentRequest request,
  );
}
