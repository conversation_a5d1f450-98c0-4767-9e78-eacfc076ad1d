import 'dart:convert';
import 'package:dio/dio.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/error/exceptions.dart';
import '../../../core/network/api_client.dart';
import '../../../core/utils/logger.dart';
import '../../models/payments/zoho_books_payment_request_model.dart';
import '../../models/payments/zoho_books_payment_session_model.dart';

/// Abstract interface for Zoho Books payment remote data source
abstract class ZohoBooksPaymentRemoteDataSource {
  /// Creates a payment link with Zoho Books payment gateway
  Future<ZohoBooksPaymentSessionModel> createPaymentLink(
    ZohoBooksPaymentRequestModel request,
  );
}

/// Implementation of Zoho Books payment remote data source
class ZohoBooksPaymentRemoteDataSourceImpl
    implements ZohoBooksPaymentRemoteDataSource {
  final ApiClient apiClient;
  final AppLogger logger;

  ZohoBooksPaymentRemoteDataSourceImpl({
    required this.apiClient,
    required this.logger,
  });

  @override
  Future<ZohoBooksPaymentSessionModel> createPaymentLink(
    ZohoBooksPaymentRequestModel request,
  ) async {
    try {
      logger.i(
        'Creating Zoho Books payment link for invoice: ${request.invoiceId}',
      );

      // Prepare the request payload for Zoho Books API
      final requestPayload = request.toJson();

      logger.d('Zoho Books payment request payload: $requestPayload');

      // Call the Zoho Books payment link creation endpoint
      final response = await apiClient.post(
        '${AppConstants.baseUrl}/zoho-books/payments/create-link',
        data: requestPayload,
      );

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.data) as Map<String, dynamic>;
        logger.i('Zoho Books payment link created successfully');
        logger.d('Response data: $jsonData');

        // Create session model with the response data
        final sessionData = {
          'invoice_id': request.invoiceId,
          'payment_link_url': jsonData['payment_link_url'],
          'amount': request.amount,
          'currency': request.currency,
          'description': request.description,
          'created_at': DateTime.now().toIso8601String(),
        };

        return ZohoBooksPaymentSessionModel.fromJson(sessionData);
      } else {
        // Enhanced error logging for debugging
        logger.e('Failed to create Zoho Books payment link:');
        logger.e('  - Status Code: ${response.statusCode}');
        logger.e('  - Response Headers: ${response.headers}');
        logger.e('  - Response Data: ${response.data}');
        logger.e(
          '  - Request URL: ${AppConstants.baseUrl}/zoho-books/payments/create-link',
        );
        logger.e('  - Request Payload: $requestPayload');

        // Try to parse error response if it's JSON
        try {
          if (response.data != null) {
            final errorData = jsonDecode(response.data) as Map<String, dynamic>;
            logger.e('  - Parsed Error: $errorData');
            if (errorData.containsKey('error')) {
              logger.e('  - Error Message: ${errorData['error']}');
            }
            if (errorData.containsKey('message')) {
              logger.e('  - API Message: ${errorData['message']}');
            }
          }
        } catch (parseError) {
          logger.e('  - Could not parse error response: $parseError');
        }

        throw ServerException();
      }
    } catch (e) {
      logger.e('Error creating Zoho Books payment link: ${e.toString()}');
      logger.e('Error type: ${e.runtimeType}');

      // Enhanced error logging for different exception types
      if (e is DioException) {
        logger.e('DioException details:');
        logger.e('  - Type: ${e.type}');
        logger.e('  - Message: ${e.message}');
        logger.e('  - Response: ${e.response?.data}');
        logger.e('  - Status Code: ${e.response?.statusCode}');
        logger.e('  - Request URL: ${e.requestOptions.path}');
        logger.e('  - Request Data: ${e.requestOptions.data}');
      }

      if (e is ServerException) {
        rethrow;
      }
      throw ServerException();
    }
  }
}
