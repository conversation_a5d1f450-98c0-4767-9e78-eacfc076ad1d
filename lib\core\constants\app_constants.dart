class AppConstants {
  //Images
  static const String imgAquaconnectLogo = 'assets/svg/aquaconnect_logo.svg';
  static const String imgPartnerLogo = 'assets/svg/aquapartner_logo.svg';
  static const String imgBanner = 'assets/images/dr_grow_banner.jpg';
  static const String imgMoonCalMar2025 = 'assets/images/moon_cal_mar_2025.png';

  //Icons
  static const String iconHomeSelected = 'assets/icons/home_selected.png';
  static const String iconHomeUnSelected = 'assets/icons/home_unselected.png';
  static const String iconLocationPointer = 'assets/icons/location_pointer.png';
  static const String iconPriceListSelected =
      'assets/icons/price_list_selected.png';
  static const String iconPriceListUnselected =
      'assets/icons/price_list_unselected.png';
  static const String iconProductsListSelected =
      'assets/icons/products_list_selected.png';
  static const String iconProductListUnselected =
      'assets/icons/products_list_unselected.png';
  static const String iconShrimp = 'assets/icons/shrimp.png';

  // Scheme Related Images
  static const String walkingMan = 'assets/svg/walking_man.svg';
  static const String baliMobileBanner =
      'assets/images/scheme/bali_mobile_banner.jpg';
  static const String baliFlightIcon = 'assets/svg/flight_bali.svg';
  static const String baliTicket1 = 'assets/images/scheme/bali_ticket_1.svg';
  static const String baliTicket2 = 'assets/images/scheme/bali_ticket_2.svg';
  static const String baliTicket3 = 'assets/images/scheme/bali_ticket_3.svg';

  // User related
  static const int maxLoginAttempts = 3;
  static const Duration sessionTimeout = Duration(hours: 24);

  // Auth Field updated
  static const String DEFAULT_COUNTRY_CODE = '+91';
  static const String DEFAULT_ISO_CODE = 'IN';

  // Cache related
  static const Duration cacheDuration = Duration(days: 7);

  static const String paymentLinkURL =
      'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/zoho/payments/create-link';

  // API related
  static const String baseUrl =
      'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api';
  //static const String baseUrl = 'https://mango-river-089f14d00.4.azurestaticapps.net/api';
  static const int apiTimeout = 30000; // milliseconds
}
