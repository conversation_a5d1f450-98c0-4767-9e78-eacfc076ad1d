<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zoho Payment</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .payment-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 400px;
            width: 100%;
        }
        .loading {
            color: #666;
            margin: 20px 0;
        }
        .error {
            color: #d32f2f;
            margin: 20px 0;
            padding: 15px;
            background-color: #ffebee;
            border-radius: 5px;
        }
        .success {
            color: #388e3c;
            margin: 20px 0;
            padding: 15px;
            background-color: #e8f5e8;
            border-radius: 5px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <h2>Processing Payment</h2>
        <div id="loading" class="loading">
            <div class="spinner"></div>
            <p>Initializing Zoho Payment...</p>
        </div>
        <div id="error" class="error" style="display: none;"></div>
        <div id="success" class="success" style="display: none;"></div>
    </div>

    <!-- Zoho Payments SDK -->
    <script src="https://static.zohocdn.com/zpay/zpay-js/v1/zpayments.js"></script>
    
    <script>
        // Get payment session data from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const sessionId = urlParams.get('sessionId');
        const amount = parseFloat(urlParams.get('amount') || '0');
        const invoiceNumber = urlParams.get('invoiceNumber') || '';
        const customerName = urlParams.get('customerName') || 'Customer';
        const customerEmail = urlParams.get('customerEmail') || '<EMAIL>';

        console.log('🚀 BRIDGE: Starting Zoho payment with:', {
            sessionId, amount, invoiceNumber, customerName, customerEmail
        });

        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error').textContent = message;
            
            // Notify Flutter about the error
            setTimeout(() => {
                window.location.href = `flutter://payment-error?message=${encodeURIComponent(message)}`;
            }, 2000);
        }

        function showSuccess(transactionId) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('success').style.display = 'block';
            document.getElementById('success').textContent = 'Payment completed successfully!';
            
            // Notify Flutter about success
            setTimeout(() => {
                window.location.href = `flutter://payment-success?transactionId=${encodeURIComponent(transactionId)}`;
            }, 1000);
        }

        function initializeZohoPayment() {
            if (!sessionId) {
                showError('Invalid payment session ID');
                return;
            }

            // Wait for ZPayments to be available
            if (typeof ZPayments === 'undefined') {
                console.log('⏳ BRIDGE: Waiting for ZPayments to load...');
                setTimeout(initializeZohoPayment, 500);
                return;
            }

            try {
                console.log('🏗️ BRIDGE: Initializing ZPayments widget');
                
                // Configuration matching your working Next.js implementation
                const config = {
                    account_id: "***********",
                    domain: "IN",
                    otherOptions: {
                        api_key: "1003.f35b9411653295bb03db1e8490dc6cdd.0f625d54ec97f4eba7bedd0dc6fc23b8"
                    }
                };

                const instance = new ZPayments(config);
                
                const options = {
                    amount: amount.toString(),
                    currency_code: "INR",
                    payments_session_id: sessionId,
                    currency_symbol: "₹",
                    business: "AquaPartner",
                    description: `Payment for AquaPartner Invoice ${invoiceNumber}`,
                    address: {
                        name: customerName,
                        email: customerEmail
                    }
                };

                console.log('🔧 BRIDGE: Payment options:', options);

                instance.requestPaymentMethod(options)
                    .then((data) => {
                        console.log('✅ BRIDGE: Payment successful:', data);
                        showSuccess(data.payment_id || data.transaction_id || 'unknown');
                    })
                    .catch((error) => {
                        console.error('❌ BRIDGE: Payment error:', error);
                        if (error.code !== "widget_closed") {
                            showError(error.message || 'Payment failed');
                        } else {
                            // User closed the widget
                            window.location.href = 'flutter://payment-cancelled';
                        }
                    })
                    .finally(async () => {
                        try {
                            await instance.close();
                            console.log('✅ BRIDGE: ZPayments instance closed');
                        } catch (closeError) {
                            console.log('⚠️ BRIDGE: Close error (non-critical):', closeError);
                        }
                    });

            } catch (error) {
                console.error('❌ BRIDGE: Initialization error:', error);
                showError('Failed to initialize payment: ' + error.message);
            }
        }

        // Start the payment process
        document.addEventListener('DOMContentLoaded', () => {
            console.log('📄 BRIDGE: DOM loaded, starting payment initialization');
            setTimeout(initializeZohoPayment, 1000); // Give ZPayments time to load
        });
    </script>
</body>
</html>
