import '../../../domain/entities/payments/payment_transaction.dart';

/// Data model for payment transaction
class PaymentTransactionModel {
  final String transactionId;
  final String paymentId;
  final String sessionId;
  final String invoiceNumber;
  final double amount;
  final String currency;
  final String status;
  final String? errorMessage;
  final DateTime timestamp;
  final Map<String, dynamic>? additionalData;

  PaymentTransactionModel({
    required this.transactionId,
    required this.paymentId,
    required this.sessionId,
    required this.invoiceNumber,
    required this.amount,
    required this.currency,
    required this.status,
    this.errorMessage,
    required this.timestamp,
    this.additionalData,
  });

  /// Creates PaymentTransactionModel from JSON response
  factory PaymentTransactionModel.fromJson(Map<String, dynamic> json) {
    return PaymentTransactionModel(
      transactionId: json['transaction_id']?.toString() ?? '',
      paymentId: json['payment_id']?.toString() ?? '',
      sessionId: json['session_id']?.toString() ?? '',
      invoiceNumber: json['invoice_number']?.toString() ?? '',
      amount: (json['amount'] ?? 0.0).toDouble(),
      currency: json['currency']?.toString() ?? 'INR',
      status: json['status']?.toString() ?? 'failed',
      errorMessage: json['error_message']?.toString(),
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'])
          : DateTime.now(),
      additionalData: json['additional_data'] as Map<String, dynamic>?,
    );
  }

  /// Converts to domain entity
  PaymentTransaction toEntity() {
    return PaymentTransaction(
      transactionId: transactionId,
      paymentId: paymentId,
      sessionId: sessionId,
      invoiceNumber: invoiceNumber,
      amount: amount,
      currency: currency,
      status: PaymentStatusExtension.fromString(status),
      errorMessage: errorMessage,
      timestamp: timestamp,
      additionalData: additionalData,
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'transaction_id': transactionId,
      'payment_id': paymentId,
      'session_id': sessionId,
      'invoice_number': invoiceNumber,
      'amount': amount,
      'currency': currency,
      'status': status,
      'error_message': errorMessage,
      'timestamp': timestamp.toIso8601String(),
      'additional_data': additionalData,
    };
  }

  /// Creates PaymentTransactionModel from domain entity
  factory PaymentTransactionModel.fromEntity(PaymentTransaction entity) {
    return PaymentTransactionModel(
      transactionId: entity.transactionId,
      paymentId: entity.paymentId,
      sessionId: entity.sessionId,
      invoiceNumber: entity.invoiceNumber,
      amount: entity.amount,
      currency: entity.currency,
      status: entity.status.value,
      errorMessage: entity.errorMessage,
      timestamp: entity.timestamp,
      additionalData: entity.additionalData,
    );
  }
}
