                        -HC:\Users\<USER>\dev\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-<PERSON><PERSON><PERSON>OID_PLATFORM=android-23
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\28.0.13004108
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\28.0.13004108
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\28.0.13004108\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\flutter_projects\aquapartner\build\app\intermediates\cxx\Debug\3z2z3t1q\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\flutter_projects\aquapartner\build\app\intermediates\cxx\Debug\3z2z3t1q\obj\x86_64
-DCMAKE_BUILD_TYPE=Debug
-BD:\flutter_projects\aquapartner\android\app\.cxx\Debug\3z2z3t1q\x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2