import 'package:equatable/equatable.dart';

/// Domain entity representing the data returned from invoice status update
class InvoiceStatusUpdateData extends Equatable {
  final String invoiceId;
  final String previousStatus;
  final String currentStatus;
  final DateTime paymentCompletedAt;
  final DateTime updatedAt;

  const InvoiceStatusUpdateData({
    required this.invoiceId,
    required this.previousStatus,
    required this.currentStatus,
    required this.paymentCompletedAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        invoiceId,
        previousStatus,
        currentStatus,
        paymentCompletedAt,
        updatedAt,
      ];
}

/// Domain entity representing the response from invoice status update API
class InvoiceStatusUpdateResponse extends Equatable {
  final bool success;
  final String message;
  final InvoiceStatusUpdateData data;

  const InvoiceStatusUpdateResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  @override
  List<Object?> get props => [
        success,
        message,
        data,
      ];

  /// Convenience getter to check if the status was successfully updated to "Paid"
  bool get isStatusUpdatedToPaid => 
      success && data.currentStatus.toLowerCase() == 'paid';

  /// Convenience getter to get the invoice ID
  String get invoiceId => data.invoiceId;

  /// Convenience getter to get the new status
  String get newStatus => data.currentStatus;

  /// Convenience getter to get the previous status
  String get previousStatus => data.previousStatus;
}
