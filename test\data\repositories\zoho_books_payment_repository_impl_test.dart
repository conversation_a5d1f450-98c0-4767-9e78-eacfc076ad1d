import 'package:aquapartner/core/error/exceptions.dart';
import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/core/network/network_info.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/data/datasources/remote/zoho_books_payment_remote_data_source.dart';
import 'package:aquapartner/data/models/payments/zoho_books_payment_request_model.dart';
import 'package:aquapartner/data/models/payments/zoho_books_payment_session_model.dart';
import 'package:aquapartner/data/repositories/zoho_books_payment_repository_impl.dart';
import 'package:aquapartner/domain/entities/payments/zoho_books_payment_request.dart';
import 'package:aquapartner/domain/entities/payments/zoho_books_payment_session.dart';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockZohoBooksPaymentRemoteDataSource extends Mock implements ZohoBooksPaymentRemoteDataSource {}
class MockNetworkInfo extends Mock implements NetworkInfo {}
class MockAppLogger extends Mock implements AppLogger {}

void main() {
  group('ZohoBooksPaymentRepositoryImpl', () {
    late ZohoBooksPaymentRepositoryImpl repository;
    late MockZohoBooksPaymentRemoteDataSource mockRemoteDataSource;
    late MockNetworkInfo mockNetworkInfo;
    late MockAppLogger mockLogger;

    setUp(() {
      mockRemoteDataSource = MockZohoBooksPaymentRemoteDataSource();
      mockNetworkInfo = MockNetworkInfo();
      mockLogger = MockAppLogger();
      repository = ZohoBooksPaymentRepositoryImpl(
        remoteDataSource: mockRemoteDataSource,
        networkInfo: mockNetworkInfo,
        logger: mockLogger,
      );

      // Register fallback values
      registerFallbackValue(ZohoBooksPaymentRequestModel(
        invoiceId: 'fallback_invoice',
        amount: 100.0,
        currency: 'INR',
        description: 'Fallback description',
      ));
    });

    group('createPaymentLink', () {
      late ZohoBooksPaymentRequest testRequest;
      late ZohoBooksPaymentRequestModel testRequestModel;
      late ZohoBooksPaymentSessionModel testSessionModel;
      late ZohoBooksPaymentSession testSession;

      setUp(() {
        testRequest = ZohoBooksPaymentRequest(
          invoiceId: 'INV001',
          amount: 1500.0,
          currency: 'INR',
          description: 'Payment for AquaPartner Invoice INV001',
        );

        testRequestModel = ZohoBooksPaymentRequestModel(
          invoiceId: 'INV001',
          amount: 1500.0,
          currency: 'INR',
          description: 'Payment for AquaPartner Invoice INV001',
        );

        testSessionModel = ZohoBooksPaymentSessionModel(
          invoiceId: 'INV001',
          paymentLinkUrl: 'https://books.zoho.in/checkout/test_token',
          amount: 1500.0,
          currency: 'INR',
          description: 'Payment for AquaPartner Invoice INV001',
          createdAt: DateTime.now(),
        );

        testSession = testSessionModel.toEntity();
      });

      test('should check if device is online', () async {
        // Arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(() => mockRemoteDataSource.createPaymentLink(any()))
            .thenAnswer((_) async => testSessionModel);

        // Act
        await repository.createPaymentLink(testRequest);

        // Assert
        verify(() => mockNetworkInfo.isConnected).called(1);
      });

      group('device is online', () {
        setUp(() {
          when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        });

        test('should return ZohoBooksPaymentSession when network is connected and call succeeds', () async {
          // Arrange
          when(() => mockRemoteDataSource.createPaymentLink(any()))
              .thenAnswer((_) async => testSessionModel);

          // Act
          final result = await repository.createPaymentLink(testRequest);

          // Assert
          expect(result, isA<Right<Failure, ZohoBooksPaymentSession>>());
          result.fold(
            (failure) => fail('Expected Right but got Left'),
            (session) {
              expect(session.invoiceId, equals('INV001'));
              expect(session.paymentLinkUrl, equals('https://books.zoho.in/checkout/test_token'));
              expect(session.amount, equals(1500.0));
            },
          );

          verify(() => mockRemoteDataSource.createPaymentLink(any())).called(1);
        });

        test('should return ServerFailure when remote data source throws ServerException', () async {
          // Arrange
          when(() => mockRemoteDataSource.createPaymentLink(any()))
              .thenThrow(ServerException());

          // Act
          final result = await repository.createPaymentLink(testRequest);

          // Assert
          expect(result, isA<Left<Failure, ZohoBooksPaymentSession>>());
          result.fold(
            (failure) => expect(failure, isA<ServerFailure>()),
            (_) => fail('Expected Left but got Right'),
          );

          verify(() => mockRemoteDataSource.createPaymentLink(any())).called(1);
          verify(() => mockLogger.e('ServerException in createPaymentLink')).called(1);
        });

        test('should return AuthFailure when remote data source throws AuthException', () async {
          // Arrange
          when(() => mockRemoteDataSource.createPaymentLink(any()))
              .thenThrow(AuthException());

          // Act
          final result = await repository.createPaymentLink(testRequest);

          // Assert
          expect(result, isA<Left<Failure, ZohoBooksPaymentSession>>());
          result.fold(
            (failure) => expect(failure, isA<AuthFailure>()),
            (_) => fail('Expected Left but got Right'),
          );

          verify(() => mockRemoteDataSource.createPaymentLink(any())).called(1);
          verify(() => mockLogger.e('AuthException in createPaymentLink')).called(1);
        });

        test('should return ServerFailure when remote data source throws DioException', () async {
          // Arrange
          when(() => mockRemoteDataSource.createPaymentLink(any()))
              .thenThrow(DioException(requestOptions: RequestOptions(path: '')));

          // Act
          final result = await repository.createPaymentLink(testRequest);

          // Assert
          expect(result, isA<Left<Failure, ZohoBooksPaymentSession>>());
          result.fold(
            (failure) => expect(failure, isA<ServerFailure>()),
            (_) => fail('Expected Left but got Right'),
          );

          verify(() => mockRemoteDataSource.createPaymentLink(any())).called(1);
          verify(() => mockLogger.e(any(that: contains('DioException in createPaymentLink')))).called(1);
        });

        test('should return ServerFailure when remote data source throws unexpected exception', () async {
          // Arrange
          when(() => mockRemoteDataSource.createPaymentLink(any()))
              .thenThrow(Exception('Unexpected error'));

          // Act
          final result = await repository.createPaymentLink(testRequest);

          // Assert
          expect(result, isA<Left<Failure, ZohoBooksPaymentSession>>());
          result.fold(
            (failure) => expect(failure, isA<ServerFailure>()),
            (_) => fail('Expected Left but got Right'),
          );

          verify(() => mockRemoteDataSource.createPaymentLink(any())).called(1);
          verify(() => mockLogger.e(any(that: contains('Unexpected error in createPaymentLink')))).called(1);
        });
      });

      group('device is offline', () {
        setUp(() {
          when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => false);
        });

        test('should return NetworkFailure when device is offline', () async {
          // Act
          final result = await repository.createPaymentLink(testRequest);

          // Assert
          expect(result, isA<Left<Failure, ZohoBooksPaymentSession>>());
          result.fold(
            (failure) => expect(failure, isA<NetworkFailure>()),
            (_) => fail('Expected Left but got Right'),
          );

          verifyNever(() => mockRemoteDataSource.createPaymentLink(any()));
          verify(() => mockLogger.w('No network connection for createPaymentLink')).called(1);
        });
      });
    });
  });
}
