import '../../../domain/entities/payments/payment_request.dart';

/// Data model for payment request
class PaymentRequestModel {
  final double amount;
  final String currencyCode;
  final String customerId;
  final String invoiceNumber;
  final String description;
  final String? customerName;
  final String? customerEmail;
  final String? customerPhone;
  final String? redirectUrl;
  final String? referenceId;
  final List<PaymentRequestMetaDataModel> metaData;

  PaymentRequestModel({
    required this.amount,
    required this.currencyCode,
    required this.customerId,
    required this.invoiceNumber,
    required this.description,
    this.customerName,
    this.customerEmail,
    this.customerPhone,
    this.redirectUrl,
    this.referenceId,
    this.metaData = const [],
  });

  /// Creates PaymentRequestModel from domain entity
  factory PaymentRequestModel.fromEntity(PaymentRequest entity) {
    return PaymentRequestModel(
      amount: entity.amount,
      currencyCode: entity.currencyCode,
      customerId: entity.customerId,
      invoiceNumber: entity.invoiceNumber,
      description: entity.description,
      customerName: entity.customerName,
      customerEmail: entity.customerEmail,
      customerPhone: entity.customerPhone,
      redirectUrl: entity.redirectUrl,
      referenceId: entity.referenceId,
      metaData: entity.metaData
          .map((item) => PaymentRequestMetaDataModel.fromEntity(item))
          .toList(),
    );
  }

  /// Converts to JSON for API request
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'amount': amount,
      'currency_code': currencyCode,
      'customer_id': customerId,
      'invoice_number': invoiceNumber,
      'description': description,
    };

    if (customerName != null) json['customer_name'] = customerName;
    if (customerEmail != null) json['customer_email'] = customerEmail;
    if (customerPhone != null) json['customer_phone'] = customerPhone;
    if (redirectUrl != null) json['redirect_url'] = redirectUrl;
    if (referenceId != null) json['reference_id'] = referenceId;
    if (metaData.isNotEmpty) {
      json['meta_data'] = metaData.map((item) => item.toJson()).toList();
    }

    return json;
  }

  /// Converts to domain entity
  PaymentRequest toEntity() {
    return PaymentRequest(
      amount: amount,
      currencyCode: currencyCode,
      customerId: customerId,
      invoiceNumber: invoiceNumber,
      description: description,
      customerName: customerName,
      customerEmail: customerEmail,
      customerPhone: customerPhone,
      redirectUrl: redirectUrl,
      referenceId: referenceId,
      metaData: metaData.map((item) => item.toEntity()).toList(),
    );
  }
}

/// Data model for payment request metadata
class PaymentRequestMetaDataModel {
  final String key;
  final String value;

  PaymentRequestMetaDataModel({
    required this.key,
    required this.value,
  });

  /// Creates PaymentRequestMetaDataModel from domain entity
  factory PaymentRequestMetaDataModel.fromEntity(PaymentRequestMetaData entity) {
    return PaymentRequestMetaDataModel(
      key: entity.key,
      value: entity.value,
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'value': value,
    };
  }

  /// Converts to domain entity
  PaymentRequestMetaData toEntity() {
    return PaymentRequestMetaData(
      key: key,
      value: value,
    );
  }
}
