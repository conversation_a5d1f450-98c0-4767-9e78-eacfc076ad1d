{"buildFiles": ["C:\\Users\\<USER>\\dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\flutter_projects\\aquapartner\\android\\app\\.cxx\\Debug\\3z2z3t1q\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\flutter_projects\\aquapartner\\android\\app\\.cxx\\Debug\\3z2z3t1q\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}