import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ZohoPaymentWebView Hosted Checkout Tests', () {
    group('Hosted Checkout URL Building', () {
      test('should build correct hosted checkout URL with session ID', () {
        const sessionId = 'test_session_123';
        const expectedBaseUrl =
            'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/checkout';

        // This would be tested by creating a test instance and calling the private method
        // For now, we verify the expected URL format
        expect(
          expectedBaseUrl,
          contains(
            'aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/checkout',
          ),
        );
        expect(sessionId, isNotEmpty);
      });

      test('should include customer parameters in URL when provided', () {
        const customerName = 'Test Customer';
        const customerEmail = '<EMAIL>';
        const invoiceNumber = 'INV-001';

        // Expected URL format with parameters for hosted checkout
        const expectedParams =
            'sessionId=test_123&customerName=Test%20Customer&customerEmail=test%40example.com&invoiceNumber=INV-001';

        expect(expectedParams, contains('sessionId='));
        expect(expectedParams, contains('customerName='));
        expect(expectedParams, contains('customerEmail='));
        expect(expectedParams, contains('invoiceNumber='));
      });
    });

    group('URL Completion Detection', () {
      test('should detect hosted completion URLs correctly', () {
        const testUrls = [
          'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/payment-success?payment_id=123',
          'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/payment-failure?error=failed',
          'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/payment-cancel',
          'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/checkout?payment_status=success',
          'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/checkout?success=true',
        ];

        for (final url in testUrls) {
          final isHostedUrl =
              url.contains(
                'aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net',
              ) &&
              (url.contains('/payment-success') ||
                  url.contains('/payment-failure') ||
                  url.contains('/payment-cancel') ||
                  url.contains('payment_status=') ||
                  url.contains('success=') ||
                  url.contains('error='));

          expect(
            isHostedUrl,
            isTrue,
            reason: 'Should detect $url as hosted completion URL',
          );
        }
      });

      test('should not detect non-hosted URLs as completion URLs', () {
        const testUrls = [
          'https://example.com/success',
          'https://other-payment.com/checkout/success',
          'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/checkout',
        ];

        for (final url in testUrls) {
          final isHostedCompletionUrl =
              url.contains(
                'aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net',
              ) &&
              (url.contains('/payment-success') ||
                  url.contains('/payment-failure') ||
                  url.contains('/payment-cancel') ||
                  url.contains('payment_status=') ||
                  url.contains('success=') ||
                  url.contains('error='));

          expect(
            isHostedCompletionUrl,
            isFalse,
            reason: 'Should not detect $url as hosted completion URL',
          );
        }
      });
    });

    group('Transaction ID Extraction', () {
      test('should extract transaction ID from various parameter names', () {
        const testCases = [
          {
            'url':
                'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/payment-success?payment_id=txn_123',
            'expected': 'txn_123',
          },
          {
            'url':
                'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/payment-success?transaction_id=txn_456',
            'expected': 'txn_456',
          },
          {
            'url':
                'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/payment-success?transactionId=txn_789',
            'expected': 'txn_789',
          },
          {
            'url':
                'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/payment-success?zpay_payment_id=zpay_123',
            'expected': 'zpay_123',
          },
        ];

        for (final testCase in testCases) {
          final uri = Uri.parse(testCase['url']!);
          final extractedId =
              uri.queryParameters['payment_id'] ??
              uri.queryParameters['transaction_id'] ??
              uri.queryParameters['txn_id'] ??
              uri.queryParameters['zpay_payment_id'] ??
              uri.queryParameters['transactionId'];

          expect(extractedId, equals(testCase['expected']));
        }
      });
    });

    group('Payment Status Detection', () {
      test('should correctly identify success URLs', () {
        const successUrls = [
          'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/payment-success',
          'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/checkout?payment_status=success',
          'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/checkout?success=true',
          'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/checkout?status=success',
        ];

        for (final url in successUrls) {
          final uri = Uri.parse(url);
          final queryParams = uri.queryParameters;

          final isSuccess =
              url.contains('/payment-success') ||
              queryParams['success'] == 'true' ||
              queryParams['payment_status'] == 'success' ||
              queryParams['status'] == 'success' ||
              queryParams['payment_status'] == 'completed';

          expect(isSuccess, isTrue, reason: 'Should identify $url as success');
        }
      });

      test('should correctly identify cancellation URLs', () {
        const cancelUrls = [
          'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/payment-cancel',
          'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/checkout?cancelled=true',
          'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/checkout?payment_status=cancelled',
          'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/checkout?status=cancelled',
        ];

        for (final url in cancelUrls) {
          final uri = Uri.parse(url);
          final queryParams = uri.queryParameters;

          final isCancelled =
              url.contains('/payment-cancel') ||
              queryParams['cancelled'] == 'true' ||
              queryParams['payment_status'] == 'cancelled' ||
              queryParams['status'] == 'cancelled';

          expect(
            isCancelled,
            isTrue,
            reason: 'Should identify $url as cancelled',
          );
        }
      });
    });
  });
}
