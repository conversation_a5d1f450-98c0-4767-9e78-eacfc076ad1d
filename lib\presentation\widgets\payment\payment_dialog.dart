import 'package:aqua_ui/aqua_ui.dart';
import 'package:flutter/material.dart';
import '../../../domain/entities/payments/payment_session.dart';
import 'zoho_payment_webview.dart';

/// Dialog for showing payment WebView
class PaymentDialog {
  /// Shows the payment dialog with WebView
  static Future<Map<String, dynamic>?> show({
    required BuildContext context,
    required PaymentSession paymentSession,
    String? customerName,
    String? customerEmail,
    String? invoiceNumber,
  }) async {
    return await showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog.fullscreen(
          child: ZohoPaymentWebView(
            paymentSession: paymentSession,
            customerName: customerName,
            customerEmail: customerEmail,
            invoiceNumber: invoiceNumber,
            onPaymentComplete: ({
              required bool isSuccess,
              String? transactionId,
              String? errorMessage,
            }) {
              Navigator.of(context).pop({
                'isSuccess': isSuccess,
                'transactionId': transactionId,
                'errorMessage': errorMessage,
              });
            },
            onCancel: () {
              Navigator.of(context).pop({
                'isSuccess': false,
                'errorMessage': 'Payment cancelled by user',
              });
            },
          ),
        );
      },
    );
  }

  /// Shows a payment confirmation dialog before starting payment
  static Future<bool> showConfirmation({
    required BuildContext context,
    required String invoiceNumber,
    required double amount,
    required String currency,
  }) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: AquaText.subheadline(
            'Confirm Payment',
            weight: AquaFontWeight.semibold,
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AquaText.body('You are about to make a payment for:'),
              const SizedBox(height: 12),
              _buildDetailRow('Invoice:', invoiceNumber),
              const SizedBox(height: 8),
              _buildDetailRow(
                'Amount:',
                '$currency ${amount.toStringAsFixed(2)}',
              ),
              const SizedBox(height: 16),
              AquaText.caption(
                'You will be redirected to Zoho Payments to complete the transaction.',
                color: acGrey300,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: AquaText.body('Cancel', color: acGrey300),
            ),
            AquaButton(
              title: 'Proceed to Payment',
              onTap: () => Navigator.of(context).pop(true),
              color: acWhiteColor,
            ),
          ],
        );
      },
    );

    return result ?? false;
  }

  /// Shows payment result dialog
  static Future<void> showResult({
    required BuildContext context,
    required bool isSuccess,
    required String invoiceNumber,
    String? transactionId,
    String? errorMessage,
  }) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                isSuccess ? Icons.check_circle : Icons.error,
                color: isSuccess ? Colors.green : Colors.red,
                size: 28,
              ),
              const SizedBox(width: 12),
              AquaText.subheadline(
                isSuccess ? 'Payment Successful' : 'Payment Failed',
                weight: AquaFontWeight.semibold,
                color: isSuccess ? acGreenColor : acRedColor,
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (isSuccess) ...[
                AquaText.body('Your payment has been processed successfully.'),
                const SizedBox(height: 12),
                _buildDetailRow('Invoice:', invoiceNumber),
                if (transactionId != null) ...[
                  const SizedBox(height: 8),
                  _buildDetailRow('Transaction ID:', transactionId),
                ],
              ] else ...[
                AquaText.body(
                  errorMessage ??
                      'Payment could not be completed. Please try again.',
                ),
                const SizedBox(height: 12),
                _buildDetailRow('Invoice:', invoiceNumber),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: AquaText.body('Ok', color: acGrey300),
            ),
          ],
        );
      },
    );
  }

  static Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: AquaText.caption(
            label,
            weight: AquaFontWeight.semibold,
            color: acNormalGreyColor,
          ),
        ),
        Expanded(child: AquaText.caption(value, color: acDarkGreyColor)),
      ],
    );
  }
}
