import 'dart:convert';

import 'package:aquapartner/core/constants/app_constants.dart';
import 'package:aquapartner/core/error/exceptions.dart';
import 'package:aquapartner/core/network/api_client.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/data/datasources/remote/zoho_books_payment_remote_data_source.dart';
import 'package:aquapartner/data/models/payments/zoho_books_payment_request_model.dart';
import 'package:aquapartner/data/models/payments/zoho_books_payment_session_model.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockApiClient extends Mock implements ApiClient {}
class MockAppLogger extends Mock implements AppLogger {}

void main() {
  group('ZohoBooksPaymentRemoteDataSourceImpl', () {
    late ZohoBooksPaymentRemoteDataSourceImpl dataSource;
    late MockApiClient mockApiClient;
    late MockAppLogger mockLogger;

    setUp(() {
      mockApiClient = MockApiClient();
      mockLogger = MockAppLogger();
      dataSource = ZohoBooksPaymentRemoteDataSourceImpl(
        apiClient: mockApiClient,
        logger: mockLogger,
      );
    });

    group('createPaymentLink', () {
      late ZohoBooksPaymentRequestModel testRequest;
      late Response<dynamic> mockResponse;

      setUp(() {
        testRequest = ZohoBooksPaymentRequestModel(
          invoiceId: 'INV001',
          amount: 1500.0,
          currency: 'INR',
          description: 'Payment for AquaPartner Invoice INV001',
        );

        mockResponse = Response<dynamic>(
          data: jsonEncode({
            'payment_link_url': 'https://books.zoho.in/checkout/test_token',
            'status': 'success',
          }),
          statusCode: 200,
          requestOptions: RequestOptions(path: ''),
        );
      });

      test('should create Zoho Books payment link successfully', () async {
        // Arrange
        when(
          () => mockApiClient.post(
            '${AppConstants.baseUrl}/zoho-books/payments/create-link',
            data: any(named: 'data'),
          ),
        ).thenAnswer((_) async => mockResponse);

        // Act
        final result = await dataSource.createPaymentLink(testRequest);

        // Assert
        expect(result, isA<ZohoBooksPaymentSessionModel>());
        expect(result.invoiceId, equals('INV001'));
        expect(
          result.paymentLinkUrl,
          equals('https://books.zoho.in/checkout/test_token'),
        );
        expect(result.amount, equals(1500.0));
        expect(result.currency, equals('INR'));
        expect(result.description, equals('Payment for AquaPartner Invoice INV001'));

        verify(
          () => mockApiClient.post(
            '${AppConstants.baseUrl}/zoho-books/payments/create-link',
            data: testRequest.toJson(),
          ),
        ).called(1);
      });

      test('should throw ServerException when API returns error status', () async {
        // Arrange
        final errorResponse = Response<dynamic>(
          data: jsonEncode({'error': 'Invalid request'}),
          statusCode: 400,
          requestOptions: RequestOptions(path: ''),
        );

        when(
          () => mockApiClient.post(
            '${AppConstants.baseUrl}/zoho-books/payments/create-link',
            data: any(named: 'data'),
          ),
        ).thenAnswer((_) async => errorResponse);

        // Act & Assert
        expect(
          () => dataSource.createPaymentLink(testRequest),
          throwsA(isA<ServerException>()),
        );

        verify(
          () => mockApiClient.post(
            '${AppConstants.baseUrl}/zoho-books/payments/create-link',
            data: testRequest.toJson(),
          ),
        ).called(1);
      });

      test('should throw ServerException when API call throws exception', () async {
        // Arrange
        when(
          () => mockApiClient.post(
            '${AppConstants.baseUrl}/zoho-books/payments/create-link',
            data: any(named: 'data'),
          ),
        ).thenThrow(Exception('Network error'));

        // Act & Assert
        expect(
          () => dataSource.createPaymentLink(testRequest),
          throwsA(isA<ServerException>()),
        );

        verify(
          () => mockApiClient.post(
            '${AppConstants.baseUrl}/zoho-books/payments/create-link',
            data: testRequest.toJson(),
          ),
        ).called(1);
      });

      test('should log appropriate messages during payment link creation', () async {
        // Arrange
        when(
          () => mockApiClient.post(
            '${AppConstants.baseUrl}/zoho-books/payments/create-link',
            data: any(named: 'data'),
          ),
        ).thenAnswer((_) async => mockResponse);

        // Act
        await dataSource.createPaymentLink(testRequest);

        // Assert
        verify(() => mockLogger.i('Creating Zoho Books payment link for invoice: INV001')).called(1);
        verify(() => mockLogger.d(any(that: contains('Zoho Books payment request payload')))).called(1);
        verify(() => mockLogger.i('Zoho Books payment link created successfully')).called(1);
        verify(() => mockLogger.d(any(that: contains('Response data')))).called(1);
      });

      test('should log error messages when payment link creation fails', () async {
        // Arrange
        when(
          () => mockApiClient.post(
            '${AppConstants.baseUrl}/zoho-books/payments/create-link',
            data: any(named: 'data'),
          ),
        ).thenThrow(Exception('Network error'));

        // Act & Assert
        expect(
          () => dataSource.createPaymentLink(testRequest),
          throwsA(isA<ServerException>()),
        );

        verify(() => mockLogger.i('Creating Zoho Books payment link for invoice: INV001')).called(1);
        verify(() => mockLogger.e(any(that: contains('Error creating Zoho Books payment link')))).called(1);
      });

      test('should handle missing payment_link_url in response', () async {
        // Arrange
        final responseWithoutUrl = Response<dynamic>(
          data: jsonEncode({
            'status': 'success',
            // Missing payment_link_url
          }),
          statusCode: 200,
          requestOptions: RequestOptions(path: ''),
        );

        when(
          () => mockApiClient.post(
            '${AppConstants.baseUrl}/zoho-books/payments/create-link',
            data: any(named: 'data'),
          ),
        ).thenAnswer((_) async => responseWithoutUrl);

        // Act
        final result = await dataSource.createPaymentLink(testRequest);

        // Assert
        expect(result.paymentLinkUrl, equals(''));
      });
    });
  });
}
