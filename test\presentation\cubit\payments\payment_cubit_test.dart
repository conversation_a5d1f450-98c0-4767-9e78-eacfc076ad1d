import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/core/services/analytics_service.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/domain/entities/customer.dart';
import 'package:aquapartner/domain/entities/invoices/invoice.dart';
import 'package:aquapartner/domain/entities/payments/payment_request.dart';
import 'package:aquapartner/domain/entities/payments/payment_session.dart';
import 'package:aquapartner/domain/entities/payments/payment_transaction.dart';
import 'package:aquapartner/domain/services/auth_service.dart';
import 'package:aquapartner/domain/usecases/payments/create_payment_session_usecase.dart';
import 'package:aquapartner/domain/usecases/payments/get_payment_status_usecase.dart';
import 'package:aquapartner/domain/usecases/payments/process_payment_usecase.dart';
import 'package:aquapartner/presentation/cubit/payments/payment_cubit.dart';
import 'package:aquapartner/presentation/cubit/payments/payment_state.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../helpers/test_helpers.dart';
import '../../../mocks/mock_analytics.dart';

// Mock classes
class MockCreatePaymentSessionUseCase extends Mock
    implements CreatePaymentSessionUseCase {}

class MockProcessPaymentUseCase extends Mock implements ProcessPaymentUseCase {}

class MockGetPaymentStatusUseCase extends Mock
    implements GetPaymentStatusUseCase {}

class MockAuthService extends Mock implements AuthService {}

class MockAppLogger extends Mock implements AppLogger {}

void main() {
  group('PaymentCubit', () {
    late PaymentCubit paymentCubit;
    late MockCreatePaymentSessionUseCase mockCreatePaymentSessionUseCase;
    late MockProcessPaymentUseCase mockProcessPaymentUseCase;
    late MockGetPaymentStatusUseCase mockGetPaymentStatusUseCase;
    late MockAuthService mockAuthService;
    late MockAppLogger mockLogger;
    late MockAnalyticsService mockAnalyticsService;

    // Test data
    late Invoice testInvoice;
    late Customer testCustomer;
    late PaymentRequest testPaymentRequest;
    late PaymentSession testPaymentSession;
    late PaymentTransaction testPaymentTransaction;

    setUpAll(() {
      registerFallbackValue(
        PaymentRequest(
          amount: 100.0,
          currencyCode: 'INR',
          customerId: 'test_customer',
          invoiceNumber: 'INV001',
          description: 'Test payment',
        ),
      );
    });

    setUp(() {
      mockCreatePaymentSessionUseCase = MockCreatePaymentSessionUseCase();
      mockProcessPaymentUseCase = MockProcessPaymentUseCase();
      mockGetPaymentStatusUseCase = MockGetPaymentStatusUseCase();
      mockAuthService = MockAuthService();
      mockLogger = MockAppLogger();
      mockAnalyticsService = MockAnalyticsService();

      // Create test data
      testCustomer = TestHelpers.createTestCustomer();

      testInvoice = Invoice(
        invoiceId: 'invoice_123',
        addressId: 'address_123',
        ageInDays: 5,
        ageTier: 'Overdue',
        balance: 1500.0,
        customerId: testCustomer.customerId,
        deliveryMode: 'Standard',
        deliveryStatus: 'Delivered',
        dueDate: DateTime.now().subtract(Duration(days: 5)),
        invoiceDate: DateTime.now().subtract(Duration(days: 10)),
        invoiceNumber: 'INV001',
        invoiceStatus: 'Overdue',
        subTotal: 1350.0,
        total: 1500.0,
        items: [],
      );

      testPaymentRequest = PaymentRequest(
        amount: testInvoice.total,
        currencyCode: 'INR',
        customerId: testCustomer.customerId,
        invoiceNumber: testInvoice.invoiceNumber,
        description:
            'Payment for AquaPartner Invoice ${testInvoice.invoiceNumber}',
        customerName: testCustomer.customerName,
        customerEmail: testCustomer.email,
        customerPhone: testCustomer.mobileNumber,
      );

      testPaymentSession = PaymentSession(
        sessionId: 'session_123',
        sessionUrl: 'https://payments.zoho.in/paymentlinks/test_token',
        paymentSessionId: 'session_123',
        currency: 'INR',
        amount: 1500.0,
        createdTime: DateTime.now().millisecondsSinceEpoch,
      );

      testPaymentTransaction = PaymentTransaction(
        transactionId: 'txn_123',
        paymentId: 'pay_123',
        sessionId: 'session_123',
        invoiceNumber: 'INV001',
        amount: 1500.0,
        currency: 'INR',
        status: PaymentStatus.success,
        timestamp: DateTime.now(),
      );

      paymentCubit = PaymentCubit(
        createPaymentSessionUseCase: mockCreatePaymentSessionUseCase,
        processPaymentUseCase: mockProcessPaymentUseCase,
        getPaymentStatusUseCase: mockGetPaymentStatusUseCase,
        authService: mockAuthService,
        logger: mockLogger,
        analyticsService: mockAnalyticsService,
      );
    });

    tearDown(() {
      paymentCubit.close();
    });

    test('initial state is PaymentInitial', () {
      expect(paymentCubit.state, equals(PaymentInitial()));
    });

    group('createPaymentSession', () {
      blocTest<PaymentCubit, PaymentState>(
        'emits [PaymentSessionLoading, PaymentSessionCreated] when successful',
        build: () {
          when(
            () => mockAuthService.getCurrentCustomer(),
          ).thenAnswer((_) async => Right(testCustomer));
          when(
            () => mockCreatePaymentSessionUseCase(any()),
          ).thenAnswer((_) async => Right(testPaymentSession));
          return paymentCubit;
        },
        act: (cubit) => cubit.createPaymentSession(testInvoice),
        expect:
            () => [
              PaymentSessionLoading(),
              PaymentSessionCreated(testPaymentSession),
            ],
        verify: (_) {
          verify(() => mockAuthService.getCurrentCustomer()).called(1);
          verify(() => mockCreatePaymentSessionUseCase(any())).called(1);
        },
      );

      blocTest<PaymentCubit, PaymentState>(
        'emits [PaymentSessionLoading, PaymentFailure] when auth service fails',
        build: () {
          when(
            () => mockAuthService.getCurrentCustomer(),
          ).thenAnswer((_) async => Left(AuthFailure()));
          return paymentCubit;
        },
        act: (cubit) => cubit.createPaymentSession(testInvoice),
        expect:
            () => [
              PaymentSessionLoading(),
              PaymentFailure(
                message: 'Unable to retrieve customer information',
                invoiceNumber: testInvoice.invoiceNumber,
              ),
              PaymentFailure(
                message: 'Customer information not found',
                invoiceNumber: testInvoice.invoiceNumber,
              ),
            ],
      );

      blocTest<PaymentCubit, PaymentState>(
        'emits [PaymentSessionLoading, PaymentFailure] when use case fails',
        build: () {
          when(
            () => mockAuthService.getCurrentCustomer(),
          ).thenAnswer((_) async => Right(testCustomer));
          when(
            () => mockCreatePaymentSessionUseCase(any()),
          ).thenAnswer((_) async => Left(ServerFailure()));
          return paymentCubit;
        },
        act: (cubit) => cubit.createPaymentSession(testInvoice),
        expect:
            () => [
              PaymentSessionLoading(),
              PaymentFailure(
                message: 'Server error occurred. Please try again later.',
                invoiceNumber: testInvoice.invoiceNumber,
              ),
            ],
      );
    });

    group('processPayment', () {
      blocTest<PaymentCubit, PaymentState>(
        'emits [PaymentProcessing, PaymentSuccess] when successful',
        build: () {
          when(
            () => mockProcessPaymentUseCase(
              sessionId: any(named: 'sessionId'),
              invoiceNumber: any(named: 'invoiceNumber'),
              amount: any(named: 'amount'),
              currency: any(named: 'currency'),
            ),
          ).thenAnswer((_) async => Right(testPaymentTransaction));
          return paymentCubit;
        },
        act:
            (cubit) => cubit.processPayment(
              sessionId: 'session_123',
              invoiceNumber: 'INV001',
              amount: 1500.0,
              currency: 'INR',
            ),
        expect:
            () => [
              PaymentProcessing(
                sessionId: 'session_123',
                invoiceNumber: 'INV001',
              ),
              PaymentSuccess(testPaymentTransaction),
            ],
      );

      blocTest<PaymentCubit, PaymentState>(
        'emits [PaymentProcessing, PaymentFailure] when use case fails',
        build: () {
          when(
            () => mockProcessPaymentUseCase(
              sessionId: any(named: 'sessionId'),
              invoiceNumber: any(named: 'invoiceNumber'),
              amount: any(named: 'amount'),
              currency: any(named: 'currency'),
            ),
          ).thenAnswer((_) async => Left(NetworkFailure()));
          return paymentCubit;
        },
        act:
            (cubit) => cubit.processPayment(
              sessionId: 'session_123',
              invoiceNumber: 'INV001',
              amount: 1500.0,
              currency: 'INR',
            ),
        expect:
            () => [
              PaymentProcessing(
                sessionId: 'session_123',
                invoiceNumber: 'INV001',
              ),
              PaymentFailure(
                message:
                    'No internet connection. Please check your network and try again.',
                invoiceNumber: 'INV001',
              ),
            ],
      );
    });

    group('getPaymentStatus', () {
      blocTest<PaymentCubit, PaymentState>(
        'emits [PaymentStatusLoading, PaymentStatusLoaded] when successful',
        build: () {
          when(
            () => mockGetPaymentStatusUseCase('txn_123'),
          ).thenAnswer((_) async => Right(testPaymentTransaction));
          return paymentCubit;
        },
        act: (cubit) => cubit.getPaymentStatus('txn_123'),
        expect:
            () => [
              PaymentStatusLoading('txn_123'),
              PaymentStatusLoaded(testPaymentTransaction),
            ],
      );

      blocTest<PaymentCubit, PaymentState>(
        'emits [PaymentStatusLoading, PaymentFailure] when use case fails',
        build: () {
          when(
            () => mockGetPaymentStatusUseCase('txn_123'),
          ).thenAnswer((_) async => Left(ServerFailure()));
          return paymentCubit;
        },
        act: (cubit) => cubit.getPaymentStatus('txn_123'),
        expect:
            () => [
              PaymentStatusLoading('txn_123'),
              PaymentFailure(
                message: 'Server error occurred. Please try again later.',
              ),
            ],
      );
    });

    group('cancelPayment', () {
      blocTest<PaymentCubit, PaymentState>(
        'emits PaymentCancelled when called',
        build: () => paymentCubit,
        act:
            (cubit) => cubit.cancelPayment(
              sessionId: 'session_123',
              invoiceNumber: 'INV001',
            ),
        expect:
            () => [
              PaymentCancelled(
                sessionId: 'session_123',
                invoiceNumber: 'INV001',
              ),
            ],
      );
    });

    group('resetPaymentState', () {
      blocTest<PaymentCubit, PaymentState>(
        'emits PaymentInitial when called',
        build: () => paymentCubit,
        act: (cubit) => cubit.resetPaymentState(),
        expect: () => [PaymentInitial()],
      );
    });
  });
}
